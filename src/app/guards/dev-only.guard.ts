import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DevOnlyGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(): boolean {
    // Check if we're in production environment
    const currentUrl = window.location.origin;
    const productionUrl = 'https://pgmlider.upl-ltd.com';

    // Only block the exact production URL, allow dev and other environments
    if (currentUrl === productionUrl) {
      console.log('Production environment detected. Blocking access to development tool.');

      // Clear any stored authentication data
      localStorage.clear();
      sessionStorage.clear();

      // Redirect to login page
      this.router.navigate(['/sign-in']);

      // Show alert to user
      alert('This testing tool is not available in production environment.');

      return false;
    }

    // Additional check using environment configuration if available
    if (environment.production && environment.baseUrl &&
        environment.baseUrl === productionUrl) {
      console.log('Production environment detected via config. Blocking access.');

      // Clear any stored authentication data
      localStorage.clear();
      sessionStorage.clear();

      // Redirect to login page
      this.router.navigate(['/sign-in']);

      // Show alert to user
      alert('This testing tool is not available in production environment.');

      return false;
    }

    // Allow access in development/staging environments (including dev.pgmlider.upl-ltd.com)
    console.log(`Allowing access from: ${currentUrl}`);
    return true;
  }
}
