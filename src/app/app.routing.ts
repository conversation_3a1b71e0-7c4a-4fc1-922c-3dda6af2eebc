import { Routes, RouterModule, Route } from "@angular/router";
import { ModuleWithProviders } from "@angular/core";
import { LoginGuard, AuthGuard, AdminGuard } from "./guards/app.guard";
import { DevOnlyGuard } from "./guards/dev-only.guard";
import { ZonesGuard } from "./guards/zones.guard";
import { RegionsGuard } from "./guards/regions.guard";
import { SbusGuard } from "./guards/sbus.guard";
import { RewardsPointsFormComponent } from "./pages/rewards-points/rewards-points-form";
import { AllBrandsComponent } from "./pages/brands/all-brands/all-brands.component";
import { BrandFormComponent } from "./pages/brands/brand-form";
import { SKUFormComponent } from "./pages/brands/sku-form";
import { AllRegionsComponent } from "./pages/regions/all-regions/all-regions.component";
import { RegionFormComponent } from "./pages/regions/region-form";
import { AllTerritoriesComponent } from "./pages/territories/all-territories/all-territories.component";
import { TerritoryFormComponent } from "./pages/territories/territory-form";
import { Pages } from "./pages/pages.component";
import { AllZonesComponent } from "./pages/zones/all-zones/all-zones.component";
import { ZoneFormComponent } from "./pages/zones/zone-form";
import { AddSchemeComponent } from "./pages/rewards-points/add-scheme/add-scheme.component";
import { UpdateAddSchemeComponent } from "./pages/rewards-points/update-add-scheme/update-add-scheme.component";
import { AllRewardsPointsComponent } from "./pages/rewards-points/all-rewards-points/all-rewards-points.component";
import { SbuComponent } from "./pages/sbu";
import { Login } from "./pages/login";
import { Dashboard } from "./pages/dashboard/dashboard.component";
import { CalibrationComponent } from "./pages/calibration/calibration.component";

let defaultPage: string = "";
let roleIDString = localStorage.getItem("roleID");
let roleID: number | null = roleIDString ? parseInt(roleIDString) : null;
switch (roleID) {
  case 1:
    defaultPage = "dashboard";
    break;
  case 2:
    defaultPage = "dashboard";
    break;
  case 3:
    defaultPage = "dashboard";
    break;
  case 4:
    defaultPage = "dashboard";
    break;
  case 5:
    defaultPage = "zones";
    break;
  case 6:
    defaultPage = "sbu";
    break;
  default:
    defaultPage = "";
    break;
}
export const routes: Routes = [
  {
    path: "privacy-policy",
    loadComponent: () =>
      import("./pages/privacy-policy/privacy-policy.component").then(
        (m) => m.PrivacyPolicyComponent
      ),
  }, 
  {
    path: 'user',
    loadComponent: () =>
      import('./pages/users/users.component').then(
        (m) => m.Users
      ),
    //  canActivate: [LoginGuard, AdminGuard],
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/users/all-users/all-users.component').then(
            (m) => m.AllUsers
          ),
      },
    ],
  }, 

  {
    path: "terms-condition",
    loadComponent: () =>
      import("./pages/terms-condition/terms-condition.component").then(
        (m) => m.TermsConditionComponent
      ),
  },
  {
    path: "",
    loadComponent: () =>
      import("./pages/login/login.component").then((m) => m.Login),
    canActivate: [AuthGuard],
  },

  {
    path: "sign-in",

    loadComponent: () =>
      import("./pages/login/login.component").then((m) => m.Login),
    canActivate: [AuthGuard],
  },
  {
    path: "forgot-password",
    loadComponent: () =>
      import("./pages/forgot-password/forgot-password.component").then(
        (m) => m.ForgotPassword
      ),
    canActivate: [AuthGuard],
  },
  {
    path: "reset-password",
    loadComponent: () =>
      import("./pages/reset-password/reset-password.component").then(
        (m) => m.ResetPassword
      ),
    canActivate: [AuthGuard],
  },
  {
    path: "not-found",
    loadComponent: () =>
      import("./pages/not-found/not-found.component").then(
        (m) => m.NotFoundComponent
      ),
  },
  // {
  //   path: "decrypt",
  //   loadComponent: () =>
  //     import("./pages/decrypt-test/decrypt-test.component").then(
  //       (m) => m.DecryptTestComponent
  //     ),
  //   canActivate: [DevOnlyGuard],
  // },

  {
    path: "",
    component: Pages,

    children: [
      {
        path: "dashboard",
        component: Dashboard,
        canActivate: [AdminGuard],
      },
      {
        path: "calibration",
        component: CalibrationComponent,
        canActivate: [AdminGuard],
      },
      {
        path: 'supports',
        loadComponent: () =>
          import('./pages/supports/supports.component').then(
            (m) => m.SupportsComponent
          ),
        // canActivate: [LoginGuard, AdminGuard],
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./pages/supports/all-support/all-support.component').then(
                (m) => m.AllSupportComponent
              ),
          },
        ],
      },
      {
        path: 'approver-management',
        loadComponent: () =>
          import('./pages/approver-management/approver-management.component').then(
            (m) => m.ApproverManagementComponent
          ),
        // canActivate: [LoginGuard, AdminGuard],
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./pages/approver-management/approver-management.component').then(
                (m) => m.ApproverManagementComponent
              ),
          },
        ],
      },
      {
        path: 'promotions-management',
        loadComponent: () =>
          import('./pages/promotions-managment/promotions').then(
            (m) => m.AllPromotionsComponent
          ),
        // canActivate: [LoginGuard, AdminGuard],
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./pages/promotions-managment/promotions').then(
                (m) => m.AllPromotionsComponent
              ),
          },
        ],
      },
      {
        path: 'users', // <-- this should match your intended route
        loadComponent: () =>
          import('./pages/users/users.component').then((m) => m.Users),
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./pages/users/all-users/all-users.component').then(
                (m) => m.AllUsers
              ),
          },
        ],
      },      

      {
        path: '',
        children: [

          {
            path: 'target-management',
            loadComponent: () =>
              import('./pages/rewards-points/target/target.component')
                .then(m => m.TargetComponent),
          },  
          {
            path: 'invoices',
            loadComponent: () =>
              import('./pages/reward-points-history/invoices')
                .then(m => m.RewardPointsHistoryComponent),
          },  
        ],
      },

      
      {
        path: "zones",
        loadComponent: () =>
          import("./pages/zones/zones.component").then((m) => m.Zones),
        canActivate: [ZonesGuard],
        children: [
          {
            path: "",
            component: AllZonesComponent,

            canActivate: [LoginGuard, ZonesGuard],
          },
          {
            path: "add-zone",
            component: ZoneFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
          {
            path: "edit-zone",
            component: ZoneFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
        ],
      },
      {
        path: "regions",
        loadComponent: () =>
          import("./pages/regions/regions.component").then(
            (m) => m.RegionsComponent
          ),
        canActivate: [RegionsGuard],
        children: [
          {
            path: "",
            component: AllRegionsComponent,

            canActivate: [LoginGuard, RegionsGuard],
          },
          {
            path: "add-region",
            component: RegionFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
          {
            path: "edit-region",
            component: RegionFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
        ],
      },
      // {
      //   path: "rewards-points",
      //   loadComponent: () =>
      //     import("./pages/rewards-points/rewards-points.component").then(
      //       (m) => m.RewardsPointsComponent
      //     ),
      //   children: [
      //     {
      //       path: "",
      //       component: AllRewardsPointsComponent,

      //       canActivate: [LoginGuard],
      //     },
      //     {
      //       path: "edit-rewards-points",
      //       component: RewardsPointsFormComponent,

      //       canActivate: [LoginGuard, AdminGuard],
      //     },
      //     {
      //       path: "addscheme",
      //       component: AddSchemeComponent,
      //       canActivate: [LoginGuard, AdminGuard],
      //     },
      //     {
      //       path: "editScheme",
      //       component: UpdateAddSchemeComponent,
      //       canActivate: [LoginGuard, AdminGuard],
      //     },
      //   ],
      // },
      {
        path: "product-details",
        loadComponent: () =>
          import("./pages/product-details/product-details.component").then(
            (m) => m.ProductDetailsComponent
          ),
      },
      // {
      //   path: "territories",
      //   loadComponent: () =>
      //     import("./pages/territories/territories.component").then(
      //       (m) => m.TerritoriesComponent
      //     ),
      //   children: [
      //     {
      //       path: "",
      //       component: AllTerritoriesComponent,

      //       canActivate: [LoginGuard],
      //     },
      //     {
      //       path: "add-territory",
      //       component: TerritoryFormComponent,

      //       canActivate: [LoginGuard, AdminGuard],
      //     },
      //     {
      //       path: "edit-territory",
      //       component: TerritoryFormComponent,

      //       canActivate: [LoginGuard, AdminGuard],
      //     },
      //   ],
      // },
      {
        path: "profile",
        loadComponent: () =>
          import("./pages/user-profile/profile.component").then(
            (m) => m.Profile
          ),
      },
      {
        path: "help",
        loadComponent: () =>
          import("./pages/help/help.component").then((m) => m.HelpComponent),
      },
      // {
      //   path: "reports",
      //   loadComponent: () =>
      //     import("./pages/reports/reports.component").then(
      //       (m) => m.ReportsComponent
      //     ),
      // },
      {
        path: "product-catalog",
        loadComponent: () =>
          import("./pages/brands/brands.component").then(
            (m) => m.BrandsComponent
          ),

        children: [
          {
            path: "",
            component: AllBrandsComponent,

            canActivate: [LoginGuard],
          },
          {
            path: "add-crop",
            component: BrandFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
          {
            path: "edit-crop",
            component: BrandFormComponent,

            canActivate: [LoginGuard, AdminGuard],
          },
          {
            path: "sku",
            component: SKUFormComponent,

            canActivate: [LoginGuard],
          },
        ],
      },
      {
        path: "notifications",
        loadComponent: () =>
          import("./pages/notifications/notifications.component").then(
            (m) => m.NotificationsComponent
          ),
      },
      {
        path: "financial-review",
        loadComponent: () =>
          import("./pages/rewards-points/financial-review/finance-review.component").then(
            (m) => m.FinanceReviewComponent
          ),
      },
    ],
    canActivate: [LoginGuard],
  },
  {
    path: "",
    component: SbuComponent,
    children: [
      {
        path: "all-sbu",
        loadChildren: () =>
          import("./pages/sbu/all-sbu/all-sbu.component").then(
            (m) => m.AllSbuComponent
          ),
        canActivate: [LoginGuard, SbusGuard],
      },
    ],
  },
  {
    path: "**",
    component: Login,
  },
];

export const routing: ModuleWithProviders<any> = RouterModule.forRoot(routes, {
  useHash: false,
});
