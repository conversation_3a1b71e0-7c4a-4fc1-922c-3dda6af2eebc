import {
  Component,
  ViewEncapsulation,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BaThemeSpinner } from '../../../theme/services/baThemeSpinner/baThemeSpinner.service';
import { GlobalEvents } from '../../../helpers/global.events';
import { UserService } from '../../../app-services/user-service';
import { Observable, Subject, debounceTime } from 'rxjs';
import * as _ from 'lodash';
import { AppConstant } from '../../../constants/app.constant';
import { ngxCsv } from 'ngx-csv/ngx-csv';
import { Utility } from 'src/app/shared/utility/utility';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn, FormControl } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
// import * as saveAs from 'file-saver';
import * as XLSX from 'xlsx';
import * as wjcCore from '@grapecity/wijmo';
import * as wjcXlsx from '@grapecity/wijmo.xlsx';
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatDividerModule } from '@angular/material/divider';
import { DeletePopupComponent } from 'src/app/modals/delete-popup/delete-popup.component';
import { EmailValidator } from '../../../theme/validators/email.validator';
interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  changeStatus: boolean;
  showStatus: boolean;
  showUpload: boolean;
  showDownload?: boolean;
  showDelete?: boolean;
  showTarget?: boolean;
}

@Component({
  selector: 'all-users',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./../users.scss'],
  templateUrl: 'all-users.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    NgxPaginationModule,
    DynamicTableComponent,
    MatMenuModule,
    MatIconModule,
    AngularMultiSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatSelectModule,
    MatInputModule,
    MatPaginatorModule,
    MatDividerModule
  ],
})
export class AllUsers {
  companies: Array<{ id: number, name: string }> = [];
  @ViewChild('addUserDialog') addUserDialog!: TemplateRef<any>;
  addUserDialogRef!: MatDialogRef<any>;
  @ViewChild('bulkUploadBox') bulkUploadBox!: TemplateRef<any>;
  @ViewChild('bulkPreviewBox') bulkPreviewBox!: TemplateRef<any>;
  @ViewChild('filterMenuDailog') filterMenuDailog!: TemplateRef<any>;
  // @ViewChild('uploadAggrement') uploadAggrement!: TemplateRef<any>;
  @ViewChild('uploadAggrement') uploadAggrement!: TemplateRef<any>;
  uploadAggrementDialogRef!: MatDialogRef<any>;
  @ViewChild('uploadAggrementFile') uploadAggrementFile!: TemplateRef<any>;
  uploadAggrementFileDialogRef!: MatDialogRef<any>;
  filterMenuDailogRef!: MatDialogRef<any>;
  @ViewChild('uplEmployeeFilterMenuDailog')
  uplEmployeeFilterMenuDailog!: TemplateRef<any>;
  uplEmployeeFilterDialogRef!: MatDialogRef<any>;
  @ViewChild('fileInput') fileInput: any;
  previewDialogRef!: MatDialogRef<any>;
  workbook!: wjcXlsx.Workbook;
  sheetIndex!: number;
  adminForm!: FormGroup;
  appUserForm!: FormGroup;
  userData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  tableData: any = [];
  exportData: any = [];
  showIndex: any = { index: null };
  startDate: Date = new Date(new Date().getFullYear(), 2, 1);
  endDate: Date = new Date(new Date().getFullYear() + 1, 2, 31);
  selectFile: { type: string; name: string; file: File }[] = [];
  targetForm!: FormGroup;
  minDate: Date | null = null;
  agreementId: any;
  agreementValue: any = [];
  agreementDataList: any = [];
  selectedAgreementStatus: any = [];
  isAgreementActive: any;
  uploadAgreementHeader: any = [
    'Upload Agreement',
    'Start Date',
    'End Date',
    'Uploaded By',
    'Uploaded On',
    'Status',
  ];
  uploadAgreementColumn: any = [
    'uploadagreement',
    'startDate',
    'endDate',
    'uploadedBy',
    'uploadedOn',
    'status',
  ];

  uploadTableData: any = []
  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 0,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showStatus: true,
    showEdit: true,
    changeStatus: true,
    productIcon: false,
    showUpload: true,
    showTarget: true,
  };

  uploadAggrementSettings: ConfigurationSettings = {
    showPagination: false,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showStatus: false,
    showEdit: false,
    changeStatus: false,
    productIcon: false,
    showUpload: false,
    showDownload: true,
    showDelete: true
  }
  agreementStatusDropdownSettings = {
    text: 'Select Status',
    enableSearchFilter: false,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    allowClear: false,
    showCheckbox: false,
  };
  allRequests: any = [];
  PCCode!: string;
  selectedOption: any;
  modelChanged: Subject<string> = new Subject<string>();
  showFileTemplate: boolean = false;
  currentUsers: any;
  userRole: any;
  userButton: any = [];
  searchedValue: string = '';
  isSearch: boolean = false;
  showOtherFilters: any = {
    showRadioFilters: true,
    showSearch: true,
    add: true,
    showdropdown1Filters: true,
    showdropdown1Title: '',
    showdropdown2Filters: true,
    showSearchiconFilters: true,
    showDistributorSignedUpDropdownFilters: false,
    showReport: false,
    export: true,
  };
  isActive: boolean = true;
  signedUpDropdown: any;
  model: string = '';
  addButtonText!: string;
  bulkUpload: string = 'Bulk Upload';
  userId: any;
  isPreview: boolean = false;
  rowNumber!: number;
  userRoleList: any = [];
  userRoleFilterList: any = [];
  filterRole: any = [];
  role: any = [];
  uplEmployeeRoleList: any = [];
  uplEmployeeRole: any = [];
  distributorCodeDataList: any = [];
  distributorCode: any = [];
  selectedDistributorCode: any;
  district: any = [];
  selectedUserRole: any = [];
  appUserRole: number = 0;
  uplEmployeeFilterRole: string = '';
  districtIdList: any = [];
  selectedDistrictId: any = [];
  isDitributor: boolean = false;
  isAdminRole: boolean = false;

  userRoleDropdownSettings = {
    text: '',
    enableSearchFilter: false,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  distributorCodeDropdownSettings = {
    text: '',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: true,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  isDistributorCodeDropdownSettings = {
    text: '',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  roleDropdownSettings = {
    text: 'Select Role',
    enableSearchFilter: false,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  uplEmployeeDropdownSettings = {
    text: 'Select Role',
    enableSearchFilter: false,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  districtDropdownSettings = {
    text: 'Select District',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: false,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false
  };

  stateDropdownSettings = {
    text: 'Select state',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false
  };
  zoneDropdownSettings = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  cityDropdownSettings = {
    text: 'Select City',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false
  };
  cropDropdownSettings = {
    text: 'Select Main Crop',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  totalCount: number = 0;
  totalRecordCount = 0;
  perPage = 20;
  currentPage = 0;
  fileSizeInKB: any;
  fileName!: string;
  isAdmin: boolean = true;
  isUplEmployee: boolean = false;
  isAppUser: boolean = false;
  selectedFile: any;
  formData: any;
  isSubmitDisabled: boolean = true;
  isActiveStatusAdmin: any;
  drawRoot: any;
  stateValue: any;
  regionValue: any;
  zoneValue: any;
  cityValue: any;
  cropValue: any;
  stateDataList: any = [];
  regionDataList: any = [];
  zoneDataList: any = [];
  cityDataList: any = [];
  cropDataList: any = [];
  selectedState: any = [];
  selectedRegion: any = [];
  selectedZone: any = [];
  selectedCity: any = [];
  selectedCrop: any = [];
  uploadedFiles: { type: string; name: string; file: File | null }[] = [];
  companyId: null | undefined;
  addressId: null | undefined;
  enableMultipleLogin: boolean = false;
  disableMultipleLoginToggle: boolean = false;
  loginUsers: Array<{ multiFirstName: string, multiLastName: string, phoneNumber: string, email: string, fromApi: boolean, countryCode: string }> = [
    { multiFirstName: '', multiLastName: '', phoneNumber: '', email: '', fromApi: false, countryCode: 'MX' }
  ];

  constructor(
    private router: Router,
    private userService: UserService,
    public toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private utility: Utility,
    public dialog: MatDialog,
    private formBuilder: FormBuilder,
    private sidebarService: SidebarServiceService,
    private rewardPointService: RewardPointsService,

  ) {
    this.setUserForm();
    this.events.setChangedContentTopText('Users Management');
    this.events.setChangedStatusText('Admin');
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth(); // 0-indexed (0 = Jan, 2 = March, 3 = April)

    const financialYearStart = currentMonth < 3 ? currentYear - 1 : currentYear;
    const financialYearEnd = currentMonth < 3 ? currentYear : currentYear + 1;

    this.startDate = new Date(financialYearStart, 3, 1); // April 1st
    this.endDate = new Date(financialYearEnd, 2, 31);    // March 31st
    this.targetForm = this.formBuilder.group({
      startDate: [this.startDate],
      endDate: [this.endDate]
    });
  }

  /**
   *  Called automatically on init
   */
  ngOnInit() {
    this.spinner.show();

    // Step 1: Try getting role from local storage immediately
    const localRole = AuthenticationHelper.getRole();

    if (localRole) {
      this.setRoleBasedConfig(localRole); // Handles UI logic
      this.spinner.hide();
    }

    // Step 2: Subscribe to userRole$ in case it comes later from API
    this.userService.userRole$.subscribe(apiRole => {
      if (apiRole && apiRole !== this.userRole) {
        this.setRoleBasedConfig(apiRole);
        this.spinner.hide();
      }
    });



    this.userService.setUserActive(this.isActive);
    this.commonAddButton();
    window.scrollTo(0, 0);
    this.getAllCustomerRole();
    this.getAgreementStatus();
    this.getRegion();
    if (this.isAdminRole) {
      this.tabChanged('isAdmin');
    } else {
      this.tabChanged('isUplEmployee');
    }
    this.targetForm = this.formBuilder.group({
      startDate: [this.startDate],
      endDate: [this.endDate]
    });
    this.appUserForm = this.formBuilder.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      mobileNumber: ['', [Validators.required, Validators.pattern('^[0-9][0-9]*$')]],
      countryCode: ['MX'], // Default value Xset to +52
      gstNumber: [''],
      email: ['', [Validators.required, EmailValidator.validate]],
      address: ['', Validators.required],
      companyname: ['', Validators.required],

    });
    this.adminForm = this.formBuilder.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, EmailValidator.validate]],
      ugdn: ['', Validators.required],
      phoneNumber: ['', [Validators.required, Validators.pattern('^[0-9][0-9]*$')]],
      role: [''],
      companyname: [''],
      address: [''],
    });
    this.modelChanged.pipe(debounceTime(400)).subscribe((model: any) => {
      if (model && model.trim()) {
        this.isSearch = true;
        this.searchedValue = model.trim();
        this.getUsersPageData(0);
      } else {
        this.searchedValue = '';
        this.getUsersPageData(0);
        this.isSearch = false;
      }
    });
  }
  private setRoleBasedConfig(role: string): void {
    this.userRole = role;
    this.isAdminRole = role === 'ADMIN';

    if (!this.isAdminRole) {
      this.configurationSettings.showActionsColumn = false;
      this.configurationSettings.showEdit = false;
      this.showOtherFilters.add = false;

      // Automatically change to UPL Employee tab if not admin
      this.tabChanged('isUplEmployee');
    } else {
      // If admin, we can keep the current tab or default to admin tab
      if (!this.isAdmin && !this.isUplEmployee && !this.isAppUser) {
        this.tabChanged('isAdmin');
      }
    }
  }

  tabChanged(tabChangeEvent: any): void {
    if (!this.isAdminRole) {
      this.configurationSettings.showActionsColumn = false;
    } else {
      this.configurationSettings.showActionsColumn = true;
    }

    if (tabChangeEvent === 'isAdmin') {
      this.events.setChangedStatusText('Admin');
      this.isAdmin = true;
      this.isUplEmployee = false;
      this.isAppUser = false;
      this.totalCount = 0;
      this.currentUsers = 'admin';
      this.addButtonText = 'Add Admin';
      this.userService.setRoleName('admin');
      this.showOtherFilters.showdropdown1Title = 'admin';
      this.userRole = 'ADMIN';
      this.PCCode = '';
      this.searchedValue = '';
      this.model = '';
      this.selectedUserRole = [];
      this.selectedDistrictId = [];
      this.uplEmployeeRole = [];
      this.uplEmployeeFilterRole = '';
      this.currentPage = 0;
      this.configurationSettings.showEdit = this.isAdminRole; // Only show edit if admin role
      this.showOtherFilters.add = this.isAdminRole; // Only show add button if admin role
      this.showOtherFilters.showdropdown1Filters = false;
      this.showOtherFilters.showDistributorSignedUpDropdownFilters = false;
      const headers: any = [
        'UGDN',
        'First Name',
        'Last Name',
        'Email',
        'Mobile No.',
      ];
      const column: any = [
        'ugdn',
        'first_name',
        'last_name',
        'email',
        'phone_no',
      ];
      const userData: any = [];
      this.setTableHeader(headers, column, userData);
      this.userDataFunc();
    } else if (tabChangeEvent === 'isUplEmployee') {
      this.events.setChangedStatusText('UPL Employee');
      this.isAdmin = false;
      this.isUplEmployee = true;
      this.isAppUser = false;
      this.totalCount = 0;
      this.userService.setRoleName('upl-employee');
      this.addButtonText = 'Add UPL-Employee';
      this.events.clearPCSelect.emit(true);
      this.currentUsers = 'upl-employee';
      this.showOtherFilters.showdropdown1Title = 'upl-employee';
      this.userRole = '';
      this.PCCode = '';
      this.searchedValue = '';
      this.model = '';
      this.selectedUserRole = [];
      this.selectedDistrictId = [];
      this.uplEmployeeRole = [];
      this.uplEmployeeFilterRole = '';
      this.currentPage = 0;
      this.configurationSettings.showEdit = this.isAdminRole; // Only show edit if admin role
      this.showOtherFilters.add = false;
      this.showOtherFilters.showdropdown1Filters = true;
      this.showOtherFilters.showDistributorSignedUpDropdownFilters = false;
      const headers: any = [
        'UGDN',
        'First Name',
        'Last Name',
        'Email',
        'Mobile No.',
        'Role',
      ];
      const column: any = [
        'ugdn',
        'first_name',
        'last_name',
        'email',
        'phone_no',
        'role',
      ];
      const userData: any = [];
      this.setTableHeader(headers, column, userData);
      this.userDataFunc();
    } else if (tabChangeEvent === 'isAppUser') {
      this.events.setChangedStatusText('Leaders');
      this.isAdmin = false;
      this.isUplEmployee = false;
      this.isAppUser = true;
      this.totalCount = 0;
      this.addButtonText = 'Add Leader';
      this.events.clearPCSelect.emit(true);
      this.currentUsers = 'leader';
      this.showOtherFilters.showdropdown1Title = 'leader';
      this.showOtherFilters.showDistributorSignedUpDropdownFilters = true;
      this.currentPage = 0;
      this.configurationSettings.showEdit = this.isAdminRole; // Only show edit if admin role
      this.showOtherFilters.add = false;
      this.PCCode = '';
      this.searchedValue = '';
      this.model = '';
      this.selectedUserRole = [];
      this.role = [];
      this.uplEmployeeFilterRole = '';
      this.filterRole = [];
      this.selectedDistrictId = [];
      this.district = [];
      this.showOtherFilters.showdropdown1Filters = true;
      const headers: any = [
        'Leader Name',
        'Leader Type',
        'Mobile No.',
        'Company Name',
        'Region',
        'Zone',
        'State',
        'City',
        'Main Crop',
        'View Doc.',
        'Agreement Status',
      ];
      const column: any = [
        'leaderName',
        'leaderType',
        'phone_no',
        'company_name',
        'region',
        'zone',
        'state',
        'city',
        'crop',
        'view_doc',
        'agreement_status',
      ];
      const userData: any = [];
      this.setTableHeader(headers, column, userData);
      this.leaderData();
    } else {
      this.events.setChangedStatusText('Admin');
      this.isAdmin = true;
      this.isUplEmployee = false;
      this.isAppUser = false;
      this.currentUsers = 'admin';
      this.addButtonText = 'Add Admin';
      this.userRole = 'ADMIN';
      this.PCCode = '';
      this.searchedValue = '';
      this.model = '';
      this.selectedUserRole = [];
      this.selectedDistrictId = [];
      this.uplEmployeeFilterRole = '';
      this.currentPage = 0;
      this.showOtherFilters.add = this.isAdminRole; // Only show add button if admin role
      this.showOtherFilters.showdropdown1Title = 'admin';
      this.showOtherFilters.showdropdown1Filters = false;
      this.showOtherFilters.showDistributorSignedUpDropdownFilters = false;
      const headers: any = [
        'UGDN3',
        'First Name',
        'Last Name',
        'Email',
        'Mobile No.',
      ];
      const column: any = [
        'ugdn',
        'first_name',
        'last_name',
        'email',
        'phone_no',
      ];
      const userData: any = [];
      this.setTableHeader(headers, column, userData);
      this.userDataFunc();
    }
  }

  commonAddButton() {
    this.userButton = [
      {
        path: '/users/add-user',
        title: ' Add User',
      },
    ];
  }

  /**
   *  Called automatically after view init
   */
  ngAfterViewInit() {
    this.events.onPCSelect.subscribe((item) => {
      this.currentPage = 0;
      if (item && item.tab == 'upl-employee') {
        if (item && item.pcCode != 'all') {
          this.PCCode = item.pcCode;
          this.userDataFunc();
        } else {
          this.PCCode = '';
          this.userDataFunc();
        }
      } else if (item && item.tab == 'distributor') {
        if (item && item.pcCode != 'all') {
          this.PCCode = item.pcCode;
        } else {
          this.PCCode = '';
        }
      }
    });

    document
      .getElementById('importFile')
      ?.addEventListener('change', (event) => {
        this.loadWorkbook();
      });

    document.addEventListener('DOMContentLoaded', () => {
      const sheetIndex = 0;
      this.drawSheet(sheetIndex);
    });
  }

  tabClicked(e: MouseEvent, index: number) {
    e.preventDefault();
    this.drawSheet(index);
  }

  loadWorkbook(selectedFile?: any) {
    let reader = new FileReader();
    reader.onload = (e) => {
      let workbook = new wjcXlsx.Workbook();
      workbook.loadAsync(<string>reader.result, (result: wjcXlsx.Workbook) => {
        this.workbook = result;
        this.drawSheet(this.workbook.activeWorksheet || 0);
      });
    };

    if (selectedFile) {
      reader.readAsDataURL(selectedFile);
    }
  }

  drawSheet(sheetIndex: number) {
    this.drawRoot = document.getElementById('tableHost');
    if (this.drawRoot) {
      this.drawRoot.textContent = '';
      this.sheetIndex = sheetIndex;
      this._drawWorksheet(this.workbook, sheetIndex, this.drawRoot, 501, 10);
    } else {
    }
  }

  _drawWorksheet(
    workbook: wjcXlsx.IWorkbook,
    sheetIndex: number,
    rootElement: HTMLElement,
    maxRows: number,
    maxColumns: number
  ) {
    if (
      !workbook ||
      !workbook.sheets ||
      sheetIndex < 0 ||
      workbook.sheets.length == 0
    ) {
      return;
    }
    //

    sheetIndex = Math.min(sheetIndex, workbook.sheets.length - 1);
    //
    if (maxRows == null) {
      maxRows = this.rowNumber;
    }
    if (maxColumns == null) {
      maxColumns = 10;
    }

    let sheet = workbook.sheets[sheetIndex],
      defaultRowHeight = 20,
      defaultColumnWidth = 60,
      tableEl = document.createElement('table');
    tableEl.style.boxShadow =
      '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)';
    let srNoColEl = document.createElement('col');
    tableEl.insertBefore(srNoColEl, tableEl.firstChild);
    srNoColEl.style.width = '40px';
    tableEl.style.width = "100%";
    tableEl.border = '1';
    tableEl.style.borderCollapse = 'collapse';
    tableEl.style.borderColor = '#ddd';
    //

    let maxRowCells = 0;
    for (let r = 0; sheet.rows && r < sheet.rows.length; r++) {
      if (sheet.rows[r] && sheet.rows[r].cells) {
        maxRowCells = Math.max(maxRowCells, sheet.rows[r].cells!.length);
      }
    }
    let columns = sheet.columns || [],
      invisColCnt = columns.filter((col) => col.visible === false).length;
    if (sheet.columns) {
      maxRowCells = Math.min(Math.max(maxRowCells, columns.length), maxColumns);
      for (let c = 0; c < maxRowCells; c++) {
        let col = columns[c];
        if (col && !col.visible) {
          continue;
        }
        let colEl = document.createElement('col');
        tableEl.appendChild(colEl);
        let colWidth = defaultColumnWidth + 'px';
        if (col) {
          this._importStyle(colEl.style, col.style!);
          if (col.autoWidth) {
            colWidth = '';
          } else if (col.width != null) {
            colWidth = col.width + 'px';
          }
        }
        colEl.style.width = colWidth;
      }
    }

    let rowCount = Math.min(maxRows, sheet.rows!.length);
    for (let r = 0; sheet.rows && r < rowCount; r++) {
      if (r === 1 || r === 2 || r === 3) {
        continue;
      }
      let row = sheet.rows[r],
        cellsCnt = 0;
      if (row && !row.visible) {
        continue;
      }
      let rowEl = document.createElement('tr');
      tableEl.appendChild(rowEl);
      let srNoCellEl = document.createElement('td');
      rowEl.appendChild(srNoCellEl);
      if (r === 0) {
        rowEl.classList.add('fixed-header');
      }
      srNoCellEl.style.textAlign = 'center';
      srNoCellEl.style.fontWeight = 'bold';
      srNoCellEl.style.borderRight = '1px solid #ddd';
      srNoCellEl.style.border = '1px solid #ddd';
      srNoCellEl.style.borderColor = '#ddd';
      srNoCellEl.style.height = '40px';
      srNoCellEl.style.color = '#285eb8';
      srNoCellEl.style.backgroundColor = '#EBF3FF';
      srNoCellEl.innerHTML = r === 0 ? 'Sr. No.' : (r - 3).toString();
      srNoCellEl.style.width = '60px';
      if (row) {
        this._importStyle(rowEl.style, row.style!);
        if (row.height != null) {
          rowEl.style.height = row.height + 'px';
        }

        if (r === 0) {
          rowEl.style.backgroundColor = '#EBF3FF';
        }
        if (r % 2 === 0) {
          rowEl.style.backgroundColor = '#F8F8F8';
        } else {
          rowEl.style.backgroundColor = '#fff';
        }
        for (let c = 0; row.cells && c < row.cells.length; c++) {
          let cell = row.cells[c],
            cellEl = document.createElement('td'),
            col = columns[c];
          if (col && !col.visible) {
            continue;
          }
          cellsCnt++;
          rowEl.appendChild(cellEl);
          if (cell) {
            this._importStyle(cellEl.style, cell.style!);
            let value = cell.value;
            cellEl.style.fontFamily = 'sans-serif';
            cellEl.style.borderRight = '1px solid #ddd';
            cellEl.style.border = '1px solid #ddd';
            cellEl.style.borderColor = '#ddd';
            cellEl.style.height = '40px';
            cellEl.style.color = '#285eb8';

            if (r === 0) {
              cellEl.style.backgroundColor = '#EBF3FF';
              cellEl.style.height = '44px';
              cellEl.style.color = '#0059b3';
            }

            if (!(value == null || value !== value)) {
              if (wjcCore.isString(value) && value.charAt(0) == "'") {
                value = value.substr(1);
              }
              let netFormat = '';
              if (cell.style && cell.style.format) {
                netFormat = wjcXlsx.Workbook.fromXlsxFormat(
                  cell.style.format
                )[0];
              }
              let fmtValue = netFormat
                ? wjcCore.Globalize.format(value, netFormat)
                : value;
              cellEl.innerHTML = wjcCore.escapeHtml(fmtValue);
            }
            if (cell.colSpan && cell.colSpan > 1) {
              cellEl.colSpan = this._getVisColSpan(columns, c, cell.colSpan);
              cellsCnt += cellEl.colSpan - 1;
              c += cell.colSpan - 1;
            }
            if (cell.note && cell.note.text !== undefined) {
              wjcCore.addClass(cellEl, 'cell-note');
              cellEl.title = cell.note.text;
            }
          }

          if (c === 0) {
            let value = cell.value;
            if (typeof value === 'string') {
              // Remove commas from the Mobile Number value
              value = value.replace(/,/g, '');
            }
            // Assign the updated value to the cell
            cellEl.innerHTML = wjcCore.escapeHtml(value);
          } else if (c === 1) {
            let value = cell.value;
            if (typeof value === 'string') {
              // Remove commas from the distributor code
              value = value.replace(/,/g, '');
            }
            // Assign the updated value to the cell
            cellEl.innerHTML = wjcCore.escapeHtml(value);
          }
        }
      }
      // pad with empty cells
      let padCellsCount = maxRowCells - cellsCnt - invisColCnt;
      for (let i = 0; i < padCellsCount; i++) {
        rowEl.appendChild(document.createElement('td'));
      }
      if (!rowEl.style.height) {
        rowEl.style.height = defaultRowHeight + 'px';
      }
    }
    // do it at the end for performance
    rootElement.appendChild(tableEl);
  }
  private _getVisColSpan(
    columns: wjcXlsx.IWorkbookColumn[],
    startFrom: number,
    colSpan: number
  ) {
    let res = colSpan;
    for (
      let i = startFrom;
      i < columns.length && i < startFrom + colSpan;
      i++
    ) {
      let col = columns[i];
      if (col && !col.visible) {
        res--;
      }
    }
    return res;
  }
  private _importStyle(
    cssStyle: CSSStyleDeclaration,
    xlsxStyle: wjcXlsx.IWorkbookStyle
  ) {
    if (!xlsxStyle) {
      return;
    }
    if (xlsxStyle.fill) {
      if (xlsxStyle.fill.color) {
        cssStyle.backgroundColor = xlsxStyle.fill.color;
      }
    }
    if (xlsxStyle.hAlign && xlsxStyle.hAlign != wjcXlsx.HAlign.Fill) {
      cssStyle.textAlign = wjcXlsx.HAlign[xlsxStyle.hAlign].toLowerCase();
    }
    let font = xlsxStyle.font;
    if (font) {
      if (font.family) {
        cssStyle.fontFamily = font.family;
      }
      if (font.bold) {
        cssStyle.fontWeight = 'bold';
      }
      if (font.italic) {
        cssStyle.fontStyle = 'italic';
      }
      if (font.size != null) {
        cssStyle.fontSize = font.size + 'px';
      }
      if (font.underline) {
        cssStyle.textDecoration = 'underline';
      }
      if (font.color) {
        cssStyle.color = font.color;
      }
    }
    this.spinner.hide();
  }

  onFileSelected(files: FileList | null) {
    if (files && files.length > 0) {
      this.isSubmitDisabled = false;
    } else {
      this.isSubmitDisabled = true;
    }
    this.isPreview = true;
    this.onFilePreviewFile(event);
    if (files && files.length > 0) {
      this.selectedFile = files[0];
      this.formData = new FormData();
      this.formData.append('file', this.selectedFile, this.selectedFile.name);
    }
  }

  onFilePreviewFile(event: any) {
    if (event?.target?.files && event.target.files.length > 0) {
      const selectedFile = event.target.files[0];
      this.fileName = selectedFile.name;
      const fileSizeInBytes = selectedFile.size; // Get file size in bytes
      this.fileSizeInKB = fileSizeInBytes / 1024;
      this.fileSizeInKB = this.fileSizeInKB.toFixed(2);
      const reader = new FileReader();
      reader.onload = (fileEvent: ProgressEvent<FileReader>) => {
        if (fileEvent?.target?.result) {
          const data = new Uint8Array(fileEvent.target.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          if (workbook.SheetNames.length > 0) {
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];

            if (sheet) {
              const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });

              rows.forEach((row, rowIndex) => {
                this.rowNumber = rowIndex + 1;
              });
            }
          }
        }
      };

      reader.readAsArrayBuffer(selectedFile);
    }
    this.loadWorkbook(event.target.files[0]);
  }

  /**
   * Called automatically after destroy of component
   */
  ngOnDestroy() {
    this.allRequests.map((request: any) => {
      if (typeof request.unsubscribe != 'undefined') {
        request.unsubscribe();
      }
    });
  }

  getAllCustomerRole() {
    this.userService.getAllCustomerRole().subscribe({
      next: (customerRole: any) => {
        customerRole = this.utility.decryptString(customerRole)
        try {
          customerRole = JSON.parse(customerRole);
        } catch (e) {
          console.error("Invalid JSON in response", e);
        }
        this.userRoleList = [];
        this.userRoleFilterList = [];
        if (Array.isArray(customerRole)) {
          customerRole.forEach((roleKey: any) => {
            if (typeof roleKey === 'string') {
              const displayName = roleKey
                ? roleKey.charAt(0).toUpperCase() + roleKey.slice(1).toLowerCase()
                : '';

              const roleInfoObj = {
                name: displayName,
                id: roleKey
              };

              this.userRoleList.push(roleInfoObj);
              this.userRoleFilterList.push(roleInfoObj);
            } else if (roleKey && typeof roleKey === 'object') {
              const key = roleKey.key || '';
              const displayName = key
                ? key.charAt(0).toUpperCase() + key.slice(1).toLowerCase()
                : '';

              const roleInfoObj = {
                name: displayName,
                id: key
              };

              this.userRoleList.push(roleInfoObj);
              this.userRoleFilterList.push(roleInfoObj);
            }
          });

        } else {
          console.warn("customerRole is not an array", customerRole);
          if (customerRole && typeof customerRole === 'object') {
            Object.keys(customerRole).forEach(key => {
              const value = customerRole[key];
              const roleKey = typeof value === 'string' ? value : key;
              const displayName = roleKey
                ? roleKey.charAt(0).toUpperCase() + roleKey.slice(1).toLowerCase()
                : '';

              const roleInfoObj = {
                name: displayName,
                id: roleKey
              };
              this.userRoleList.push(roleInfoObj);
              this.userRoleFilterList.push(roleInfoObj);
            });
          }
        }
        if (this.userRoleList.length === 0 || !this.userRoleList.some((r: any) => r.id.toUpperCase() === 'ADMIN')) {
          this.userRoleList.push({
            name: 'Admin',
            id: 'ADMIN'
          });
          this.userRoleFilterList.push({
            name: 'Admin',
            id: 'ADMIN'
          });
        }
      },
      error: (errorResponse: any) => {
        let error: any = errorResponse.error;

        try {
          error = JSON.parse(error);
        } catch (e) {
          console.error("Error parsing error response", e);
        }

        if (error.message === 'Full authentication is required to access this resource') {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(error.message || 'An error occurred while fetching roles');
        }
      },
    });
  }



  setUserForm(values?: any) {
    if (this.currentUsers === 'leader') {
      this.appUserForm = this.formBuilder.group({
        distributorCode: [{ value: '', disabled: false }],
        firmName: [{ value: '', disabled: false }],
        firstName: [{ value: '', disabled: false }, Validators.required],
        lastName: [{ value: '', disabled: false }, Validators.required],
        mobileNumber: [{ value: '', disabled: false }, [Validators.required, Validators.pattern('^[0-9][0-9]*$')]],
        countryCode: [{ value: 'MX', disabled: false }],
        gstNumber: [{ value: '', disabled: false }],
        companyname: [{ value: '', disabled: false }, Validators.required],
        address: [{ value: '', disabled: false }, Validators.required],
        email: [{ value: '', disabled: false }, [Validators.required, EmailValidator.validate]],
      });
    } else {
      this.adminForm = this.formBuilder.group({
        firstName: [{ value: values?.firstName || '', disabled: true }],
        lastName: [{ value: values?.lastName || '', disabled: true }],
        email: [{ value: values?.email || '', disabled: true }],
        ugdn: [{ value: values?.code || '', disabled: true }],
        phoneNumber: [{ value: values?.phone || '', disabled: true }],
        role: [values?.role || ''], // No validators
        companyname: [{ value: '', disabled: true }],
        address: [{ value: '', disabled: false }],

      });
    }
  }


  editAppUserForm(values: any) {
    const data = { id: values.id };
    this.spinner.show();

    this.userService.getUserById(this.userId).subscribe({
      next: (userData: any) => {
        try {
          let parsedUserData = typeof userData === 'string' ? JSON.parse(userData) : userData;

          if (parsedUserData?.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedUserData.encryptedBody);
            parsedUserData = JSON.parse(decrypted);
          }

          if (parsedUserData && Object.keys(parsedUserData).length > 0) {
            // Store company and address IDs
            this.companyId = parsedUserData?.company?.id || null;
            this.addressId = parsedUserData?.address?.id || null;

            if (this.userId) {
              this.appUserForm.get('email')?.disable();
              this.appUserForm.get('mobileNumber')?.disable();
              this.appUserForm.get('countryCode')?.disable();
            }

            this.appUserForm.patchValue({
              firstName: parsedUserData?.firstName || '',
              lastName: parsedUserData?.lastName || '',
              mobileNumber: parsedUserData?.mobileNo || '',
              gstNumber: parsedUserData?.gst_no || '',
              email: parsedUserData?.email || '',
              companyname: parsedUserData?.company?.name || '',
              address: parsedUserData?.address?.name || ''
            });

            this.loginUsers = [];
            if (Array.isArray(parsedUserData.childContacts) && parsedUserData.childContacts.length > 0) {
              this.enableMultipleLogin = true;
              this.disableMultipleLoginToggle = true; // ✅ New flag to control checkbox disabling
              this.loginUsers = parsedUserData.childContacts.map((contact: any) => ({
                id: contact.id || null,
                isDeleted: contact.isDeleted,
                multiFirstName: contact.firstName || '',
                multiLastName: contact.lastName || '',
                phoneNumber: contact.mobileNo || '',
                email: contact.email || '',
                fromApi: true
              }));
            } else {
              this.enableMultipleLogin = false;
              this.disableMultipleLoginToggle = false;
              this.loginUsers = [{
                multiFirstName: '',
                multiLastName: '',
                phoneNumber: '',
                email: '',
                fromApi: false,
                countryCode: 'MX'

              }];
            }
          }

          // Dropdowns
          if (parsedUserData?.state) {
            this.stateValue = [{
              id: parsedUserData.state.id,
              name: parsedUserData.state.name,
              code: parsedUserData.state.code
            }];
            this.getCityByID(parsedUserData?.zone?.id);
          }

          if (parsedUserData.city) {
            this.cityValue = [{
              id: parsedUserData.city.id,
              name: parsedUserData.city.name
            }];
          }

          if (parsedUserData.region) {
            this.regionValue = [{
              id: parsedUserData.region.id,
              name: parsedUserData.region.name
            }];
            this.getZoneByID(parsedUserData?.region?.id);
          }

          if (parsedUserData.zone) {
            this.zoneValue = [{
              id: parsedUserData.zone.id,
              name: parsedUserData.zone.name,
              code: parsedUserData.zone.code
            }];
            this.getStateByID(parsedUserData?.zone?.id);
          }

          if (parsedUserData.crop) {
            this.cropValue = [{
              id: parsedUserData.crop.id,
              name: parsedUserData.crop.name
            }];
          }

          if (parsedUserData.rfcDocUrl) {
            const rfcFileName = parsedUserData.rfcDocUrl.split('/').pop();
            this.uploadedFiles.push({
              type: 'rfcDoc',
              name: rfcFileName,
              file: null
            });
          }

          if (parsedUserData.addressDocUrl) {
            const addressFileName = parsedUserData.addressDocUrl.split('/').pop();
            this.uploadedFiles.push({
              type: 'addressDoc',
              name: addressFileName,
              file: null
            });
          }

          if (parsedUserData.idUrl) {
            const idFileName = parsedUserData.idUrl.split('/').pop();
            this.uploadedFiles.push({
              type: 'idDoc',
              name: idFileName,
              file: null
            });
          }
        } catch (err) {
          console.error('❌ Decryption or parsing error:', err);
        } finally {
          this.spinner.hide();
        }
      },

      error: (errorResponse: any) => {
        this.events.setChangedContentTopText('Edit User');
        this.toastr.error(AppConstant.USER_FETCH_ERROR);
        this.router.navigate(['users']);
        this.spinner.hide();

        const errorMsg = errorResponse.status;
        if (+errorMsg === 401 || +errorMsg === 404) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(AppConstant.USER_FETCH_ERROR);
        }
      }
    });
  }

  onClickAddForm(event: any, tabName: any) {
    localStorage.removeItem('editUserId');
    if (tabName === 'admin') {
      this.onAddFromClick(event, tabName);
    } else if (tabName === 'upl-employee') {
      this.onAddFromClick(event, tabName);
    } else if (tabName === 'leader') {
      this.onAddFromClick(event, tabName);
    }
  }

  onBulkUpload(event: any, tabName: any) {
    if (tabName === 'admin') {
      this.onBulkUploadDialog(event, tabName);
    } else if (tabName === 'upl-employee') {
      this.onBulkUploadDialog(event, tabName);
    } else if (tabName === 'leader') {
      this.onPreviewShow(event);
    }
  }

  onBulkUploadDialog(data: object, addUsers?: string) {
    if (this.currentUsers === 'admin') {
      this.rewardPointService._disabledSidebar.emit(true);
      let sidebarValue = this.sidebarService.getBooleanValue();
      this.rewardPointService._openedPopup.emit(true);
      this.addUserDialogRef = this.dialog.open(this.bulkUploadBox, {
        width: '30%',
        height: '250px',
        disableClose: false,
        panelClass: 'confirm-dialog-container',
        data: data,
        hasBackdrop: true,
      });
      this.addUserDialogRef.afterClosed().subscribe((result: any) => {
        this.rewardPointService._disabledSidebar.emit(false);
        this.rewardPointService._sidebarPin.emit(false);
      });
    } else if (this.currentUsers === 'upl-employee') {
      this.rewardPointService._disabledSidebar.emit(true);
      let sidebarValue = this.sidebarService.getBooleanValue();
      this.rewardPointService._openedPopup.emit(true);
      this.addUserDialogRef = this.dialog.open(this.bulkUploadBox, {
        width: '30%',
        height: '250px',
        disableClose: false,
        panelClass: 'confirm-dialog-container',
        data: data,
        hasBackdrop: true,
      });
      this.addUserDialogRef.afterClosed().subscribe((result: any) => {
        this.rewardPointService._disabledSidebar.emit(false);
        this.rewardPointService._sidebarPin.emit(false);
      });
    } else if (this.currentUsers === 'leader') {
      this.rewardPointService._disabledSidebar.emit(true);
      let sidebarValue = this.sidebarService.getBooleanValue();
      this.rewardPointService._openedPopup.emit(true);
      this.addUserDialogRef = this.dialog.open(this.bulkUploadBox, {
        width: '30%',
        height: '200px',
        disableClose: false,
        panelClass: 'confirm-dialog-container',
        data: data,
        hasBackdrop: true,
      });
      this.addUserDialogRef.afterClosed().subscribe((result: any) => {
        this.rewardPointService._disabledSidebar.emit(false);
        this.rewardPointService._sidebarPin.emit(false);
      });
    }
  }
  /**
   * Triggered when user click on the status icon in the action column
   * @param data
   */
  onAddFromClick(data: object, addUsers?: string) {

    this.adminForm.get('email')?.enable();
    this.adminForm.get('ugdn')?.enable();

    this.selectedUserRole = [];
    this.role = [];
    this.userId = AuthenticationHelper.getUserEditId();
    if (this.userId) {
      if (this.currentUsers === 'admin') {
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.getUserByID(this.userId);
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '50%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: this.userData,
          hasBackdrop: true,
        });
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists when modal is closed
          this.clearAllFieldsForAdd();
          this.adminForm.reset();
        });
      } else if (this.currentUsers === 'upl-employee') {
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.getUserByID(this.userId);
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '50%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: data,
          hasBackdrop: true,
        });
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists when modal is closed
          this.clearAllFieldsForAdd();
          this.appUserForm.reset();
        });
      } else if (this.currentUsers === 'leader') {
        this.distributorCode = [];
        this.selectedDistributorCode = 0;
        this.isDitributor = true;
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.editAppUserForm(data);
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '65%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: data,
          hasBackdrop: true,
        });
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists when modal is closed
          this.clearAllFieldsForAdd();
          this.appUserForm.reset();
        });
        this.getAllCustomerRole();
      }
    } else {
      if (data && addUsers === 'admin') {
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '50%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: data,
          hasBackdrop: true,
        });
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists when modal is closed
          this.clearAllFieldsForAdd();
          this.adminForm.reset();
        });
      } else if (data && addUsers === 'upl-employee') {
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '50%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: data,
          hasBackdrop: true,
        });
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists when modal is closed
          this.clearAllFieldsForAdd();
          this.appUserForm.reset();
        });
      } else if (data && addUsers === 'leader') {
        this.spinner.show();
        this.rewardPointService._disabledSidebar.emit(true);
        let sidebarValue = this.sidebarService.getBooleanValue();
        this.rewardPointService._openedPopup.emit(true);
        this.setUserForm();
        this.addUserDialogRef = this.dialog.open(this.addUserDialog, {
          width: '65%',
          disableClose: false,
          panelClass: 'confirm-dialog-container',
          data: data,
          hasBackdrop: true,
        });
        this.spinner.hide();
        this.addUserDialogRef.afterClosed().subscribe((result: any) => {
          this.rewardPointService._disabledSidebar.emit(false);
          this.rewardPointService._sidebarPin.emit(false);
          // Clear all fields including data lists for add mode
          this.clearAllFieldsForAdd();
          this.appUserForm.reset();
          this.enableMultipleLogin = false;
          this.disableMultipleLoginToggle = false;
          this.role = [];
          this.selectedDistributorCode = 0;
          this.isDitributor = false;
          this.distributorCode = [];
        });
      }
    }
  }
  /**
   * Method for getting specific user details by id
   * @param id
   */
  getUserByID(id: any) {
    this.spinner.show();
    this.userService.getAdminEmoployeeDetailsById(this.userId).subscribe({
      next: (userData: any) => {
        userData = this.utility.decryptString(userData)
        userData = (userData);
        userData = JSON.parse(userData);
        let data = userData;
        if (data) {
          this.role = [];
          const apiRole = userData.role || '';

          if (apiRole) {
            const formattedName = apiRole.charAt(0).toUpperCase() + apiRole.slice(1).toLowerCase();
            const roleObj = {
              name: formattedName,
              id: apiRole
            };
            this.role.push(roleObj);
          }

          this.userData = {
            code: userData.code ? userData.code : '',
            firstName: userData.firstName ? userData.firstName : '',
            lastName: userData.lastName ? userData.lastName : '',
            email: userData.email ? userData.email : '',
            phone: userData.mobileNo ? userData.mobileNo : '',
            id: userData.id ? userData.id : '',
            role: userData.role ? userData.role : ''
          };
        }
        this.setUserForm(this.userData);
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.events.setChangedContentTopText('Edit User');
        this.toastr.error(AppConstant.USER_FETCH_ERROR);
        this.router.navigate(['users']);
        this.spinner.hide();
        let errorMsg = errorResponse.status;
        if (+errorMsg === 401 || +errorMsg === 404) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(AppConstant.USER_FETCH_ERROR);
        }
      },
    });
  }

  /**
   * Method for getting specific user details by id
   * @param id
   */
  getAppUserById(id: any) {
    this.userService.getAppUserDetailsById(this.userId).subscribe({
      next: (appUserDetails: any) => {
        try {
          // Step 1: Decrypt the initial string response
          const decryptedResponse = this.utility.decryptString(appUserDetails);

          // Step 2: Parse decrypted JSON
          let parsedData = JSON.parse(decryptedResponse);

          // Step 3: If the parsed data contains an encryptedBody, decrypt it as well
          if (parsedData?.encryptedBody) {
            const decryptedBody = this.utility.decrypt(parsedData.encryptedBody);
            parsedData = JSON.parse(decryptedBody);
          }

          // Step 4: Map the parsed data to the form model
          if (parsedData) {
            this.userData = {
              distributorCode: parsedData.distributorCode || '',
              name: parsedData.name || '',
              proprietaryName: parsedData.proprietaryName || '',
              mobileNo: parsedData.mobileNo || '',
              panNo: parsedData.panNo || '',
              role: parsedData.customerType || '',
              id: parsedData.id || '',
            };

            this.editAppUserForm(this.userData);
          } else {
            console.warn('Parsed app user data is empty');
          }

        } catch (err) {
          console.error('Error decrypting or parsing app user details:', err);
          this.toastr.error('Failed to load user details');
        } finally {
          this.spinner.hide();
        }
      },

      error: (errorResponse: any) => {
        this.spinner.hide();
        this.toastr.error('Error while fetching user details');
        console.error('Request error:', errorResponse);
      },
    });
  }


  onCloseAdminForm() {
    this.adminForm.reset();
    this.selectedUserRole = [];
    if (this.addUserDialogRef) {
      this.addUserDialogRef.close();
    }
  }

  onCloseAppUserForm() {
    this.appUserForm.reset();
    this.loginUsers = []; // clear child login form array
    this.enableMultipleLogin = false;
    this.disableMultipleLoginToggle = false;
    this.clearAllFieldsForAdd(); // Use the comprehensive clearing method
    this.selectedUserRole = [];
    this.role = [];
    this.selectedDistributorCode = 0;
    this.isDitributor = false;
    this.distributorCode = [];
    if (this.addUserDialogRef) {
      this.addUserDialogRef.close();
    }
  }

  closePreview() {
    this.addUserDialogRef.close();
  }

  /**
   *  Set table header
   */
  setTableHeader(header: any, column: any, userData?: any) {
    this.tableHead = header;
    this.tableColName = column;
    this.tableData = userData;
  }

  onFilterUserRoleSelect(event: any) {
    this.selectedUserRole = [];
    if (!_.includes(this.selectedUserRole, event.id)) {
      this.selectedUserRole.push(event.id);
    }
    this.appUserRole = event.id;
  }

  onFilterUserRoleDeselection(event: any) {
    if (_.includes(this.selectedUserRole, event.name)) {
      _.remove(this.selectedUserRole, (item) => item === event.name);
    }
    this.appUserRole = 0;
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  selectedAllFilterUserRole(event: any) {
    if (event && event.length) {
      this.selectedUserRole = [];
      this.selectedUserRole = _.map(event, 'name');
    }
    this.appUserRole = 0;
  }

  deSelectedAllFilterUserRole(event: any) {
    this.selectedUserRole = [];
    this.role = [];
    this.filterRole = [];
    this.appUserRole = 0;
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  onUserRoleSelect(event: any) {
    this.selectedUserRole = [];
    if (!_.includes(this.selectedUserRole, event.id)) {
      this.selectedUserRole.push(event.id);
    }
    this.appUserRole = event.id;
    if (event.name == 'Retailer') {
      this.distributorCode = [];
      this.isDitributor = true;
    } else if (event.name !== 'Retailer') {
      this.isDitributor = false;
    }
  }

  onUserRoleDeselection(event: any) {
    if (_.includes(this.selectedUserRole, event.name)) {
      _.remove(this.selectedUserRole, (item) => item === event.name);
    }
    this.appUserRole = 0;
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  selectedAllUserRole(event: any) {
    if (event && event.length) {
      this.selectedUserRole = [];
      this.selectedUserRole = _.map(event, 'name');
    }
    this.appUserRole = 0;
  }

  onUserRoleDeselectionAll(event: any) {
    this.appUserRole = 0;
  }


  deSelectedAllUserRole(event: any) {
    this.selectedUserRole = [];
    this.role = [];
    this.appUserRole = 0;
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  onUplEmployeeRoleSelect(event: any) {
    this.uplEmployeeFilterRole = event.role;
  }

  onUplEmployeeRoleDeselection(event: any) {
    this.uplEmployeeFilterRole = '';
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.cancelEmployeeFilter();
  }

  deSelectedAllUplEmployeeRole(event: any) {
    this.uplEmployeeFilterRole = '';
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.cancelEmployeeFilter();
  }

  onDistributorCodeSelect(event: any) {
    this.selectedDistributorCode = event.name;
  }

  onDistributorCodeDeselection(event: any) {
    this.selectedDistributorCode = '';
  }
  onDistributorCodeDeselectionAll(event: any) {
    this.selectedDistributorCode = '';
  }
  // agreement filter selection
  onAgreementStatusSelect(event: any) {

    this.selectedAgreementStatus = event.name;
  }

  onAgreementStatusAllSelect(event: any) {
    if (event && event.length) {
      this.selectedAgreementStatus = [];
      this.selectedAgreementStatus = _.map(event, 'id');
    }
  }

  onAgreementStatusDeselection(event: any) {
    this.selectedAgreementStatus = [];
  }

  onAgreementStatusDeselectionAll(event: any) {
    this.selectedAgreementStatus = [];
    this.agreementValue = []
  }

  onDistrictIdSelect(event: any) {
    if (!_.includes(this.selectedDistrictId, event.id)) {
      this.selectedDistrictId.push(event.id);
    }
  }

  onDistrictIdDeselect(event: any) {
    if (_.includes(this.selectedDistrictId, event.id)) {
      _.remove(this.selectedDistrictId, (item) => item === event.id);
    }
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  selectedAllDistrictId(event: any) {
    if (event && event.length) {
      this.selectedDistrictId = [];
      this.selectedDistrictId = _.map(event, 'id');
    }
  }
  deSelectedAllDistrictId(event: any) {
    this.selectedDistrictId = [];
    this.district = [];
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  cancelEmployeeFilter() {
    if (this.uplEmployeeFilterRole !== '') {
      this.uplEmployeeFilterDialogRef.close();
    } else {
    }
    this.uplEmployeeFilterDialogRef.close();
    this.selectedDistrictId = [];
    this.uplEmployeeFilterRole = '';
    this.uplEmployeeRole = [];
    this.selectedUserRole = [];
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
  }

  appUserCancelFilter() {
    this.filterMenuDailogRef.close();
    const hadFilters = this.selectedDistrictId.length ||
      this.selectedAgreementStatus.length ||
      this.agreementValue.length ||
      this.selectedUserRole.length ||
      this.district.length ||
      this.role.length;

    this.selectedDistrictId = [];
    this.selectedAgreementStatus = [];
    this.agreementValue = [];
    this.selectedUserRole = [];
    this.district = [];
    this.role = [];

    if (hadFilters) {
      if (this.currentUsers === 'leader') {
        this.leaderData();
      } else {
        this.userDataFunc();
      }
    }
  }


  filterApply() {
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.filterMenuDailogRef.close();
  }

  employeeFilterApply() {
    if (this.currentUsers === 'leader') {
      this.leaderData();
    } else {
      this.userDataFunc();
    }
    this.uplEmployeeFilterDialogRef.close();
  }


  private formatRole(role: string): string {
    return role ? role.charAt(0).toUpperCase() + role.slice(1).toLowerCase() : 'NA';
  }

  private mapUser(user: any, type: 'admin' | 'upl' | 'regular' | 'app'): any {
    const base = {
      id: user.id || 'NA',
      ugdn: user.code || 'NA',
      first_name: this.utility.toUpperCaseUtil(user.firstName) || 'NA',
      last_name: this.utility.toUpperCaseUtil(user.lastName) || 'NA',
      email: user.email || 'NA',
      phone_no: user.mobileNo || 'NA',
      is_active: user.active || false,
    };

    switch (type) {
      case 'admin':
        return base;

      case 'upl':
      case 'regular':
        return {
          ...base,
          role: this.formatRole(user.role),
          state: user.state?.name || 'NA',
          company_name: user.company?.name || 'NA'
        };
      case 'app':
        const DEFAULT_VALUE = 'NA';
        const VIEW_DOC_PATH = '../../../../assets/img/viewDoc.svg';
        const firstName = user.firstName || DEFAULT_VALUE;
        const address = user.address || DEFAULT_VALUE;
        const companyName = user.company?.name ||
          (user.companies?.length > 0
            ? user.companies.map((company: { name: string }) => company.name).join(', ')
            : DEFAULT_VALUE);
        const viewDoc = VIEW_DOC_PATH;
        const lastName = user.lastName || DEFAULT_VALUE;

        return {
          id: user.id || DEFAULT_VALUE,
          leader: firstName && lastName ? `${firstName} ${lastName}` : DEFAULT_VALUE,
          leaderName: firstName && lastName ? this.utility.toUpperCaseUtil(`${firstName} ${lastName}`) : DEFAULT_VALUE,
          user_name: user.proprietaryName || DEFAULT_VALUE,
          grower: user.grower,
          leaderType: user.grower === false ? 'Leader' : 'Grower',
          first_name: this.utility.toUpperCaseUtil(firstName),
          last_name: this.utility.toUpperCaseUtil(lastName),
          district: user.district?.name || DEFAULT_VALUE,
          city: user.city?.name || DEFAULT_VALUE,
          state: user.state?.name || (user.city?.state?.name || DEFAULT_VALUE),
          region: user.region?.name || DEFAULT_VALUE,
          zone: user.zone?.name || DEFAULT_VALUE,
          distributor_code: user.distributorCode || DEFAULT_VALUE,
          gst_no: user.gstNo || '',
          crop: (user.crop?.name ? this.utility.capitalizeWords(user.crop.name.split('-').slice(0, 2).join('-')) : DEFAULT_VALUE),
          view_doc: viewDoc,
          phone_no: user.mobileNo || DEFAULT_VALUE,
          is_active: user.active || false,
          pan_no: user.panNo || DEFAULT_VALUE,
          role: this.formatRole(user.role),
          company_name: this.utility.toUpperCaseUtil(companyName),
          address: address || DEFAULT_VALUE,
          idUrl: user.idUrl || DEFAULT_VALUE,
          rfcDocUrl: user.rfcDocUrl || DEFAULT_VALUE,
          addressDocUrl: user.addressDocUrl || DEFAULT_VALUE,

          agreement_status: this.utility.capitalizeWords(user.agreementStatus),
          targetStatus: user.targetStatus || DEFAULT_VALUE,
        };

      default:
        return base;
    }
  }

  userDataFunc(page?: number): void {
    window.scrollTo(0, 0);
    this.spinner.show();

    const getRoleFormat = () => {
      switch (this.currentUsers) {
        case 'admin': return 'ADMIN';
        case 'upl-employee': return 'UPL_EMPLOYEE';
        case 'leader': return 'LEADER';
        default: return '';
      }
    };

    const apiRole = getRoleFormat();

    this.configurationSettings.showEdit = !!this.isActive;

    // Only make API call if user has permission for the requested role
    if (apiRole === 'ADMIN' && !this.isAdminRole) {
      this.spinner.hide();
      return;
    }

    this.userService.getUserManagementData({
      isAdmin: false,
      role: apiRole,
      profitCenterCode: '',
      agreementValue: this.selectedAgreementStatus ? this.selectedAgreementStatus : 'ACTIVE',
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: page ? page - 1 : 0,
      searchedValue: encodeURIComponent(this.searchedValue)
        ? encodeURIComponent(this.searchedValue)
        : '',
      isActive: this.isActive,
      unPaged: false
    }).subscribe({
      next: (res: any) => {
        try {
          // First check if response is a string and parse it if needed
          let parsedResponse = res;
          if (typeof res === 'string') {
            parsedResponse = JSON.parse(res);
          }

          // Check if response has encryptedBody property
          let usersData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            usersData = JSON.parse(decrypted);
          } else {
            // Use the response directly
            usersData = parsedResponse;
          }

          this.spinner.hide();
          const content = usersData?.content || [];
          this.configurationSettings.totalRecordCount = usersData.totalElements;
          if (this.currentUsers === 'admin') {
            const filtered = content.filter((user: any) => user.role?.toLowerCase() === 'admin');
            this.tableData = filtered.map((user: any) => this.mapUser(user, 'admin'));
          } else if (this.currentUsers === 'upl-employee') {
            this.tableData = content.map((user: any) => this.mapUser(user, 'upl'));
          } else {
            this.tableData = content.map((user: any) => this.mapUser(user, 'regular'));
          }
          this.totalCount = usersData.totalElements
          this.events.setChangedContentTopText(`Users Management`);
        } catch (error) {
          console.error('Error processing user data:', error);
          this.spinner.hide();
          this.toastr.error('Error loading user data');
        }
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
        if(this.currentUsers === 'admin') {
                  try {
          let decryptedError;
          if (typeof errorResponse.error === 'string') {
            decryptedError = JSON.parse(errorResponse.error);
          } else if (errorResponse.error && errorResponse.error.encryptedBody) {
            const decrypted = this.utility.decrypt(errorResponse.error.encryptedBody);
            decryptedError = JSON.parse(decrypted);
          } else {
            decryptedError = errorResponse.error;
          }

          const errorCode = +decryptedError.status;
          if (errorCode === 401 || errorCode === 404) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          } else {
            this.toastr.error(decryptedError.message || 'An error occured');
          }
        } catch (error) {
          console.error('Error processing error response:', error);
          this.toastr.error('An unexpected error occurred');
        }
        }
      }
    });
  }



  leaderData(page?: number): void {
    window.scrollTo(0, 0);
    this.spinner.show();
    const data = {
      isAdmin: false,
      role: 'LEADER',
      profitCenterCode: '',
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedValue,
      unPaged: false,
      agreementValue: this.selectedAgreementStatus ? this.selectedAgreementStatus : 'ACTIVE',
      customerTypeId: this.selectedUserRole.length ? this.selectedUserRole : 0,
      districts: this.selectedDistrictId.length ? this.selectedDistrictId : '',
      isActive: this.isActive,
    };

    this.userService.getUserManagementData(data).subscribe({
      next: (res: any) => {
        try {
          let parsedResponse = typeof res === 'string' ? JSON.parse(res) : res;

          if (parsedResponse?.encryptedBody) {
            const decryptedString = this.utility.decrypt(parsedResponse.encryptedBody);
            parsedResponse = JSON.parse(decryptedString);
          }

          const content = parsedResponse?.content || [];

          if (this.currentUsers === 'leader') {
            const filtered = content.filter((user: any) => user.role?.toLowerCase() === 'leader');
            this.tableData = filtered.map((user: any) => this.mapUser(user, 'app'));
          }

          this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
          this.totalCount = parsedResponse.totalElements;
          this.events.setChangedContentTopText(`Users Management`);
        } catch (error) {
          console.error('Decryption/Parsing error:', error);
        } finally {
          this.spinner.hide();
        }
      },
      error: () => {
        this.spinner.hide();
      }
    });

  }

  /**
   * Method for the page change event
   * @param page
   */
  getUsersPageData(page: any) {

    this.configurationSettings.currentPage = page;
    this.currentPage = page;
    if (this.currentUsers === 'admin' || this.currentUsers === 'upl-employee') {
      this.userDataFunc(this.currentPage);
    } else if (this.currentUsers === 'leader') {
      this.leaderData(this.currentPage);
    }
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.perPage = event.pageSize;
    if (this.currentUsers === 'leader') {
      this.leaderData(this.currentPage);
    } else {
      this.userDataFunc(this.currentPage);
    }
  }

  /**
   * Method for routing to edit or add user
   * @param event
   */
  userDetails(event: any) {
    AuthenticationHelper.setUserEditId(event.id);
    if (this.currentUsers === 'admin') {
      this.onAddFromClick(event);
    } else if (this.currentUsers === 'upl-employee') {
      this.onAddFromClick(event);
    } else if (this.currentUsers === 'leader') {
      this.onAddFromClick(event);
    }
  }

  /**
   * Method for getting results on the basis of search query
   * @param searchString
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    this.getUsersPageData(0);
    this.isSearch = false;
  }

  ChangeStatus(event: any) {
    if (event) {
      this.changeStatus(event);
    }
  }

  /**
   * Method for exporting data in CSV format
   * @param event
   */
  onExport(event: any) {
    if (event) {
      this.getExportData();
      // Need to remove after testing global export logic 
      // if (this.currentUsers == 'admin' || this.currentUsers == 'upl-employee') {
      //   this.getUsersExportData();
      // } else if (this.currentUsers == 'leader') {
      //   this.getAppUsersExportData();
      // }
    }
  }

  /**
   * Triggered when status radio button is changed
   * @param event
   */
  onStatusFiltersChange(event: any) {
    if (event) {
      this.searchedValue = '';
      this.isActive = event.isActive ? event.isActive : false;
      this.getUsersPageData(1);
      this.userService.setUserActive(this.isActive);
    }
  }

  /**
   * To update user status
   * @param event
   */
  changeStatus(event: any) {
    if (!event) return;

    const userRole = this.currentUsers;
    const encryptedData = this.utility.encryptString({
      active: !event.is_active,
      id: event.id
    });

    // Admin or upl-employee
    if (userRole === 'admin' || userRole === 'upl-employee') {
      const updateUserStatus = this.userService.updateUserStatus(encryptedData);
      updateUserStatus.subscribe({
        next: (newdata: any) => {
          try {
            newdata = this.utility.decryptString(newdata);
            newdata = JSON.parse(newdata);
            this.toastr.success(newdata.message);
          } catch (e) {
            console.error('❌ Decryption failed:', e);
            this.toastr.error('Failed to process response');
          }
          this.getUsersPageData(this.currentPage);
          this.events.onUserStatusChange.emit(true);
        },
        error: (errorResponse: any) => {
          this.handleEncryptedError(errorResponse); // Decrypts and shows error if encrypted
          this.spinner.hide();
        }
      });

    } else if (userRole === 'leader') {
      const data = {
        active: !event.is_active,
        id: event.id
      };
      const updateDistributorStatus = this.userService.updateAppUsersStatus(data);
      updateDistributorStatus.subscribe({
        next: (newdata: any) => {
          try {
            newdata = this.utility.decryptString(newdata);
            newdata = JSON.parse(newdata);
            this.toastr.success(newdata.message);
          } catch (e) {
            console.error('❌ Decryption failed:', e);
            this.toastr.error('Failed to process response');
          }
          this.getUsersPageData(this.currentPage);
          this.events.onUserStatusChange.emit(true);
        },
        error: (errorResponse: any) => {
          this.handleEncryptedError(errorResponse);
          this.spinner.hide();
        }
      });

    } else {
      const updateRetailerStatus = this.userService.updateRetailerStatus(encryptedData);
      updateRetailerStatus.subscribe({
        next: (newdata: any) => {
          try {
            newdata = this.utility.decryptString(newdata);
            newdata = JSON.parse(newdata);
            this.toastr.success(newdata.message);
          } catch (e) {
            console.error('❌ Decryption failed:', e);
            this.toastr.error('Failed to process response');
          }
          this.getUsersPageData(this.currentPage);
          this.events.onUserStatusChange.emit(true);
        },
        error: (errorResponse: any) => {
          this.handleEncryptedError(errorResponse);
          this.spinner.hide();
        }
      });
    }
  }

  handleEncryptedError(errorResponse: any) {
    try {
      const encryptedMsg = errorResponse?.error?.message;

      if (typeof encryptedMsg === 'string') {
        const decrypted = this.utility.decryptString(encryptedMsg);
        const parsed = JSON.parse(decrypted);
        this.toastr.error(parsed.message || 'Something went wrong');
      } else {
        this.toastr.error(AppConstant.USER_UPDATE_STATUS_ERROR);
      }
    } catch (err) {
      console.error('🔐 Decryption failed:', err);
      this.toastr.error(AppConstant.USER_UPDATE_STATUS_ERROR);
    }
  }


  /**
   * Method for exporting the users data in CSV format
   */

  // Global Method to Export 

  getExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();

    const getRoleFormat = () => {
      switch (this.currentUsers) {
        case 'admin': return 'ADMIN';
        case 'upl-employee': return 'UPL_EMPLOYEE';
        case 'leader': return 'LEADER';
        default: return '';
      }
    };

    const apiRole = getRoleFormat();

    // Only make export API call if user has permission for the requested role
    if (apiRole === 'ADMIN' && !this.isAdminRole) {
      this.spinner.hide();
      return;
    }

    this.exportData = [];
    let apiCall;
    let fileName = '';
    let headers: string[] = [];
    let options: any = {
      fieldSeparator: ',',
      quoteStrings: '"',
      decimalseparator: '.',
      showLabels: true,
    };

    const data = {
      isAdmin: this.currentUsers === 'admin',
      profitCenterCode: this.PCCode || '',
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      searchedValue: encodeURIComponent(this.searchedValue || ''),
      role: apiRole,
      isActive: this.isActive,
      unPaged: true,
    };
    if (apiRole === 'LEADER') {
      apiCall = this.userService.getAllUsersExportData(data);
      fileName = 'Leader Details';
      headers = [
        'Leader Name',
        'Leader Type',
        'Mobile Number',
        'Company Name',
        'Region',
        'Zone',
        'State',
        'City',
        'Main Crop',
        'Agreement Status',
        'Status',
      ];

      apiCall.subscribe({
        next: (res: any) => {
          let decrypted = this.utility.decryptString(res);
          let result = JSON.parse(decrypted);
          if (result?.content) {
            result.content.forEach((user: any) => {
              const name = _.get(user, 'firstName', '-') + ' ' + _.get(user, 'lastName', '-');
              this.exportData.push({
                leaderName: this.utility.toUpperCaseUtil(name) || 'NA',
                leaderType: user.grower ? 'Grower' : 'Leader',
                phone_no: user.mobileNo || 'NA',
                company_name: this.utility.toUpperCaseUtil(user.company?.name) || 'NA',
                region: user.region?.name || 'NA',
                zone: user.zone?.name || 'NA',
                state: user.state?.name || 'NA',
                city: user.city?.name || 'NA',
                crop: this.utility.toUpperCaseUtil(user?.crop?.name) || 'NA',
                agreementStatus: user?.agreementStatus || 'NA',
                is_active: user.active ? 'Active' : 'Inactive',
              });
            });

            options.headers = headers;
            new ngxCsv(this.exportData, fileName, options);
          } else {
            this.toastr.warning('No data available');
          }
          this.spinner.hide();
        },
        error: () => this.spinner.hide(),
      });

    } else {
      apiCall = this.userService.getAllUsersExportData(data);
      fileName =
        apiRole === 'ADMIN'
          ? 'Admin Details'
          : apiRole === 'UPL_EMPLOYEE'
            ? 'UPL Employee Details'
            : 'User Details';

      headers = [
        'UGDN',
        'First Name',
        'Last Name',
        'Email',
        'Mobile No.',
        'Role',
        'Status',
      ];

      apiCall.subscribe({
        next: (res: any) => {
          res = this.utility.decryptString(res);
          const result = JSON.parse(res);
          if (result?.content?.length) {
            result.content.forEach((user: any) => {
              this.exportData.push({
                ugdn: _.get(user, 'code', 'NA'),
                first_name: _.get(user, 'firstName', '-'),
                last_name: _.get(user, 'lastName', 'NA'),
                email: _.get(user, 'email', 'NA'),
                phone_no: _.get(user, 'mobileNo', 'NA'),
                role: _.get(user, 'role', 'NA'),
                is_active: user.active ? 'Active' : 'Inactive',
              });
            });

            options.headers = headers;
            new ngxCsv(this.exportData, fileName, options);
          } else {
            this.toastr.warning('No data available');
          }
          this.spinner.hide();
        },
        error: () => this.spinner.hide(),
      });
    }
  }


  getUsersExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();

    const getRoleFormat = () => {
      switch (this.currentUsers) {
        case 'admin': return 'ADMIN';
        case 'upl-employee': return 'UPL_EMPLOYEE';
        case 'leader': return 'LEADER';
        default: return '';
      }
    };

    const apiRole = getRoleFormat();
    const data = {
      isAdmin: this.currentUsers === 'admin',
      profitCenterCode: this.PCCode || '',
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      searchedValue: encodeURIComponent(this.searchedValue || ''),
      role: apiRole,
      isActive: this.isActive,
      unPaged: true,
    };

    this.userService.getAllUsersExportData(data).subscribe({
      next: (exportResData: any) => {
        exportResData = this.utility.decryptString(exportResData)
        const exportsData = JSON.parse(exportResData);
        this.exportData = [];

        if (exportsData?.content?.length) {
          exportsData.content.forEach((user: any) => {
            const isActiveStatus = user.active ? 'Active' : 'Inactive';

            const exportObj = {
              ugdn: _.get(user, 'code', 'NA'),
              first_name: _.get(user, 'firstName', '-'),
              last_name: _.get(user, 'lastName', 'NA'),
              email: _.get(user, 'email', 'NA'),
              phone_no: _.get(user, 'phoneNo', 'NA'),
              role: _.get(user, 'role', 'NA'),
              is_active: isActiveStatus,
            };

            this.exportData.push(exportObj);
          });

          const options = {
            fieldSeparator: ',',
            quoteStrings: '"',
            decimalseparator: '.',
            showLabels: true,
            headers: [
              'UGDN',
              'First Name',
              'Last Name',
              'Email',
              'Mobile No.',
              'Role',
              'Status',
            ],
          };

          const fileName =
            apiRole === 'ADMIN'
              ? 'Admin Details'
              : apiRole === 'UPL_EMPLOYEE'
                ? 'UPL Employee Details'
                : apiRole === 'LEADER'
                  ? 'Leader Details'
                  : 'User Details';

          new ngxCsv(this.exportData, fileName, options);
        } else {
          this.toastr.warning('No data available');
        }

        this.spinner.hide();
      },
      error: () => {
        this.spinner.hide();
      },
    });
  }

  /**
   * Method for exporting the distributors data in CSV format
   */
  isActiveStatusDistributor: any;
  getAppUsersExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();
    let data = {
      isActive: this.isActive,
      unPaged: true,
      searchedValue: encodeURIComponent(this.searchedValue)
        ? encodeURIComponent(this.searchedValue)
        : '',
      customerTypeId: this.selectedUserRole.length ? this.selectedUserRole : 0,
      districts: this.selectedDistrictId.length ? this.selectedDistrictId : '',
    };
    const exports = this.userService.getAppUsersExportData(data);
    exports.subscribe({
      next: (exportsDataRes: any) => {
        this.exportData = [];
        let exportsData = JSON.parse((exportsDataRes));
        if (exportsData && exportsData.content) {
          exportsData.content.forEach((exportInfo: any) => {
            if (exportInfo.active == true) {
              this.isActiveStatusDistributor = 'Active';
            } else {
              this.isActiveStatusDistributor = 'InActive';
            }
            let exportObj = {
              leaderName: exportInfo.name ? this.utility.toUpperCaseUtil(exportInfo.name) : 'NA',
              user_name: exportInfo.proprietaryName
                ? this.utility.toUpperCaseUtil(exportInfo.proprietaryName)
                : 'NA',
              district: exportInfo.district.name
                ? exportInfo.district.name
                : 'NA',
              distributor_code: exportInfo.distributorCode
                ? exportInfo.distributorCode
                : 'NA',
              gst_no: exportInfo.gstNo ? exportInfo.gstNo : '',
              phone_no: exportInfo.mobileNo ? exportInfo.mobileNo : 'NA',
              role: exportInfo.customerType.name
                ? this.utility.formatString(exportInfo.customerType.name)
                : 'NA',
              is_active: exportInfo.active ? 'Active' : 'InActive',
            };
            this.exportData.push(exportObj);
          });
          let options = {
            fieldSeparator: ',',
            quoteStrings: '"',
            decimalseparator: '.',
            showLabels: true,
            headers: [
              'Leader Name',
              'User Name',
              'District',
              'Distributor Code',
              'GST Number',
              'Mobile No.',
              'Role',
              'Status',
            ],
          };
          new ngxCsv(this.exportData, 'App Users Details', options);
        } else {
          this.toastr.warning('No data available');
        }
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
      },
    });
  }

  onSubmit(formValues: any) {
    const selectedRole = this.role[0]?.id || '';
    this.spinner.show();
    if (this.userId) {
      if (this.currentUsers === 'admin') {
        // Admin user update logic (unchanged)
        let requestPayload = {
          email: (this.adminForm.controls['email'].value),
          code: (this.adminForm.controls['ugdn'].value),
          firstName: (this.adminForm.controls['firstName'].value),
          lastName: (this.adminForm.controls['lastName'].value),
          phoneNo: (this.adminForm.controls['phoneNumber'].value),
          role: 'ADMIN',
          id: this.userId ? (this.userId) : '',
        };
        this.spinner.show();

        this.userService.updateUser(requestPayload).subscribe({
          next: (userDetails: any) => {
            userDetails = this.utility.decryptString(userDetails)
            userDetails = JSON.parse((userDetails));
            this.userDataFunc();
            this.toastr.success(userDetails.message);
            this.spinner.hide();
            this.addUserDialogRef.close();
            this.adminForm.reset();
          },
          error: (errorResponse: any) => {
            errorResponse = this.utility.decryptString(errorResponse)
            this.toastr.success(errorResponse.message);
            this.spinner.hide();
          },
        });
      } else if (this.currentUsers === 'upl-employee') {
        // UPL employee update logic (unchanged)
        let requestPayload = {
          email: (this.adminForm.controls['email'].value),
          code: (this.adminForm.controls['ugdn'].value),
          firstName: (this.adminForm.controls['firstName'].value),
          lastName: (this.adminForm.controls['lastName'].value),
          phoneNo: (this.adminForm.controls['phoneNumber'].value),
          role: selectedRole || '',
          id: this.userId ? (this.userId) : '',
        };
        this.userService.updateUser(requestPayload).subscribe({
          next: (userDetails: any) => {
            this.toastr.success('User Updated Successfully');
            this.userDataFunc();
            this.spinner.hide();
            this.addUserDialogRef.close();
            this.adminForm.reset();
          },
          error: (errorResponse: any) => {
            this.spinner.hide();

            errorResponse = this.utility.decryptString(errorResponse.error);
            errorResponse = JSON.parse(errorResponse);
            this.toastr.error(errorResponse.message);
          },
        });
      } else {
        // Leader/customer update logic - UPDATED to include all fields with IDs
        // this.selectedDistributorCode =
        //   this.selectedDistributorCode === 0 ||
        //     this.selectedDistributorCode === undefined
        //     ? null
        //     : this.selectedDistributorCode.toString();

        // Create a complete request payload with all fields including IDs
        let requestBody: any = {
          mobileNo: this.appUserForm.controls['mobileNumber'].value,
          countryCode: this.appUserForm.controls['countryCode'].value,
          distributorCode: this.selectedDistributorCode,
          customerType: {
            id: this.appUserRole,
          },
          isWeb: true,
          gstNo: this.appUserForm.controls['gstNumber'].value,
          id: this.userId,
          firstName: formValues.firstName,
          lastName: formValues.lastName,
          name: formValues.firmName,
          email: formValues.email,
          company: {
            id: this.companyId || null,  // Include company ID if available
            name: formValues.companyname
          },
          address: {
            id: this.addressId || null,  // Include address ID if available
            name: formValues.address
          },
          state: this.selectedState ? { id: this.selectedState.id } : null,
          city: this.selectedCity ? { id: this.selectedCity.id } : null,
          crop: this.selectedCrop ? { id: this.selectedCrop.id } : null,
          zone: this.selectedZone ? { id: this.selectedZone.id } : null,
          region: this.selectedRegion ? { id: this.selectedRegion.id } : null
        };

        // Only include childContacts if there are valid login users
        if (this.loginUsers && this.loginUsers.length > 0) {
          const validUsers = this.loginUsers.filter((user: any) =>
            user.multiFirstName || user.multiLastName || user.phoneNumber || user.email
          );

          if (validUsers.length > 0) {
            requestBody.childContacts = validUsers.map((user: any) => ({
              id: user.id || null,
              isDeleted: user.isDeleted,
              firstName: user.multiFirstName,
              lastName: user.multiLastName,
              mobileNo: user.phoneNumber,
              email: user.email
            }));
          }
        }
        this.spinner.show();
        this.userService.updateAppUsers(requestBody).subscribe({
          next: (userDetails: any) => {
            try {
              if (typeof userDetails === 'string') {
                const decrypted = this.utility.decryptString(userDetails);
                userDetails = JSON.parse(decrypted);
              }

              if (userDetails.success) {
                this.toastr.success(userDetails.message);
                this.spinner.hide();
                this.onCloseAppUserForm();
                this.leaderData();
              } else {
                this.spinner.hide();
                this.toastr.error(userDetails.message);
              }
            } catch (e) {
              console.error('Error in processing response:', e);
              this.spinner.hide();
              this.toastr.error('Something went wrong while processing response');
            }
          },

          error: (error: any) => {
            this.spinner.hide();
            try {
              if (error.error && typeof error.error === 'string') {
                try {
                  const decrypted = this.utility.decryptString(error.error);
                  const errorObj = JSON.parse(decrypted);
                  this.toastr.error(errorObj.message || 'Update failed');
                } catch (decryptError) {
                  console.warn('Failed to decrypt error:', decryptError);
                  try {
                    const errorObj = JSON.parse(error.error);
                    this.toastr.error(errorObj.message || 'Update failed');
                  } catch (parseError) {
                    this.toastr.error('Update failed');
                  }
                }
              } else if (error.message) {
                this.toastr.error(error.message);
              } else {
                this.toastr.error('Update failed');
              }
            } catch (e) {
              console.error('Unexpected error:', e);
              this.toastr.error('Update failed');
            }
          }
        });
      }
    } else {
      if (this.currentUsers === 'admin') {

        let requestPayload = {
          email: (this.adminForm.controls['email'].value),
          code: (this.adminForm.controls['ugdn'].value),
          firstName: (
            this.adminForm.controls['firstName'].value
          ),
          lastName: (
            this.adminForm.controls['lastName'].value
          ),
          phoneNo: (
            this.adminForm.controls['phoneNumber'].value
          ),
          role: ('ADMIN'),
        };
        this.spinner.show();
        this.userService.addUser(requestPayload).subscribe({

          next: (userDetails: any) => {
            const decrypted = this.utility.decryptString(userDetails);
            userDetails = JSON.parse((decrypted));
            this.userDataFunc();
            this.toastr.success(userDetails.message);
            this.spinner.hide();
            this.addUserDialogRef.close();
            this.adminForm.reset();
          },
          error: (errorResponse: any) => {
            errorResponse = this.utility.decryptString(errorResponse.error);
            errorResponse = JSON.parse(errorResponse);
            this.toastr.error(errorResponse.message);
            this.spinner.hide();
          },
        });
      } else if (this.currentUsers === 'upl-employee') {
        let requestPayload = {
          email: (this.adminForm.controls['email'].value),
          code: (this.adminForm.controls['ugdn'].value),
          firstName: (
            this.adminForm.controls['firstName'].value
          ),
          lastName: (
            this.adminForm.controls['lastName'].value
          ),
          phoneNo: (
            this.adminForm.controls['phoneNumber'].value
          ),
          role: '',
        };
        this.userService.addUser(requestPayload).subscribe({
          next: (userDetails: any) => {
            const decrypted = this.utility.decryptString(userDetails);
            userDetails = JSON.parse(decrypted);
            this.toastr.success(userDetails.message);
            this.spinner.hide();
            this.addUserDialogRef.close();
            this.userDataFunc();
            this.adminForm.reset();
          },
          error: (errorResponse: any) => {
            this.spinner.hide();
            errorResponse = this.utility.decryptString(errorResponse.error);
            const decryptedError = this.utility.decryptString(errorResponse);
            errorResponse = JSON.parse(decryptedError);
            this.toastr.error(errorResponse.message);
          },
        });
      } else {
        const requestBody: any = {
          mobileNo: this.appUserForm.controls['mobileNumber'].value,
          countryCode: this.appUserForm.controls['countryCode'].value,
          firstName: formValues.firstName,
          lastName: formValues.lastName,
          email: formValues.email,
          state: { id: this.selectedState.id },
          company: { name: formValues.companyname },
          address: { name: formValues.address },
          city: { id: this.selectedCity.id },
          crop: { id: this.selectedCrop.id },
          zone: { id: this.selectedZone.id },
          region: { id: this.selectedRegion.id }
        };

        // Only include childContacts if there are valid login users
        if (this.loginUsers && this.loginUsers.length > 0) {
          const validUsers = this.loginUsers.filter((user: any) =>
            user.multiFirstName || user.multiLastName || user.phoneNumber || user.email
          );

          if (validUsers.length > 0) {
            requestBody.childContacts = validUsers.map((user: any) => ({
              firstName: user.multiFirstName,
              lastName: user.multiLastName,
              mobileNo: user.phoneNumber,
              email: user.email
            }));
          }
        }
        const formData = new FormData();
        const encrypted = this.utility.encryptString(JSON.stringify(requestBody));
        const encryptedString = typeof encrypted === 'string' ? encrypted : encrypted.encryptedBody;
        formData.append('customer', encryptedString);


        // 📁 Append files as-is without encryption
        const rfcFile = this.uploadedFiles.find(f => f.type === 'rfcDoc');
        if (rfcFile?.file) {
          formData.append('rfcDoc', rfcFile.file);
        }

        const addressFile = this.uploadedFiles.find(f => f.type === 'addressDoc');
        if (addressFile?.file) {
          formData.append('addressDoc', addressFile.file);
        }

        const idFile = this.uploadedFiles.find(f => f.type === 'idDoc');
        if (idFile?.file) {
          formData.append('idDoc', idFile.file);
        }

        this.userService.addUserData(formData).subscribe({
          next: (userDetails: any) => {
            try {
              const decrypted = this.utility.decryptString(userDetails);
              const parsed = JSON.parse(decrypted);

              if (parsed.success) {
                this.toastr.success('Successfully Added Leader');
                this.adminForm.reset();
                this.leaderData();
                this.addUserDialogRef.close();
              } else {
                const decryptedMessage = this.utility.decryptString(parsed.message || '');
                this.toastr.error(decryptedMessage || 'Operation failed');
              }
            } catch (err) {
              this.toastr.error('Invalid response format.');
            } finally {
              this.spinner.hide();
              this.selectedUserRole = [];
            }
          },
          error: (errorResponse: any) => {
            this.spinner.hide();
            let decryptedMessage = 'An unexpected error occurred.';
            try {
              const responseText = errorResponse?.error;
              const decrypted = this.utility.decryptString(responseText);
              const parsed = JSON.parse(decrypted);
              decryptedMessage = parsed.message || decryptedMessage;
            } catch (e) {
              // fallback already applied
            }
            this.toastr.error(decryptedMessage);
          },
        });
      }
    }
  }

  adminUplEmployeeFormSubmit(formValues: any) {
    const selectedRole = this.role[0]?.id || '';
    const requestPayload: any = {
      email: this.adminForm.controls['email'].value,
      code: this.adminForm.controls['ugdn'].value,
      firstName: this.adminForm.controls['firstName'].value,
      lastName: this.adminForm.controls['lastName'].value,
      phoneNo: this.adminForm.controls['phoneNumber'].value,
      role: selectedRole || '', // dynamically assigned role
    };

    if (this.userId) {
      requestPayload.id = this.userId;

      this.spinner.show();
      this.userService.updateUser(requestPayload).subscribe({
        next: (userDetails: any) => {
          userDetails = this.utility.decryptString(userDetails)
          userDetails = JSON.parse(userDetails);
          this.toastr.success(userDetails.message);
          this.spinner.hide();
          this.addUserDialogRef.close();
          this.adminForm.reset();
          this.userDataFunc();
        },
        error: (errorResponse: any) => {
          try {
            const decryptedText = this.utility.decryptString(errorResponse?.error);
            const parsedError = JSON.parse(decryptedText);
            this.toastr.error(parsedError.message || 'Something went wrong');
          } catch (e) {
            this.toastr.error('Failed to process error response');
          } finally {
            this.spinner.hide();
          }
        },
      });
    } else {
      this.spinner.show();
      this.userService.addUser(requestPayload).subscribe({
        next: (userDetails: any) => {
          userDetails = this.utility.decryptString(userDetails)
          userDetails = JSON.parse(userDetails);
          this.toastr.success(userDetails.message);
          this.spinner.hide();
          this.addUserDialogRef.close();
          this.adminForm.reset();
          this.userDataFunc();
        },
        error: (errorResponse: any) => {
          errorResponse = this.utility.decryptString(errorResponse)
          errorResponse = JSON.parse(errorResponse.error);
          this.toastr.error(errorResponse.message);
          this.spinner.hide();
        },
      });
    }
  }


  sanitizeInput(input: string): string {
    // Remove HTML tags
    let sanitizedValue = input.replace(/<[^>]*>/g, '');

    // Remove restricted characters
    sanitizedValue = sanitizedValue.replace(/[&^*#$!@()%]/g, '');

    return sanitizedValue;
  }

  // Event handler for input field
  onInputChange(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const sanitizedValue = this.sanitizeInput(inputValue);
    inputElement.value = sanitizedValue;
  }

  // Event handler for paste event
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = event.clipboardData!.getData('text/plain');
    const sanitizedValue = this.sanitizeInput(pastedText);

    // Check if the pasted text contains special characters
    if (!/^ [a - zA - Z\s] * $ /.test(sanitizedValue)) {
      // If it contains special characters, do not allow the paste
      return;
    }

    document.execCommand('insertText', false, sanitizedValue);
  }

  errorMessages = {
    firstName: [{ type: 'required', message: 'First Name is required' }],
    lastName: [{ type: 'required', message: 'Last Name is required' }],
    email: [
      { type: 'required', message: 'Email is required' },
      { type: 'email', message: 'Invalid email format' },
    ],
    ugdn: [{ type: 'required', message: 'UGDN is required' }],
    phoneNumber: [{ type: 'required', message: 'Mobile Number is required' }],
  };

  funRestName(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      // this.toastr.error('Double space is not allowed');
    }

    var k;
    k = event.charCode;
    if ((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32) {
    } else {
      // this.toastr.error('Number and special characters' + ' ' + 'not allowed');
    }

    return (k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32;
  }




  funEmailValidation(event: any) {
    const allowedKeys = [
      64,
      46,
      45,
      95,
    ];

    const k = event.charCode || event.keyCode;

    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }

    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      (k >= 48 && k <= 57) ||
      allowedKeys.includes(k)
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }


  withoutSpace(event: any) {
    const inputValue: string = event.target.value;

    // Prevent leading space
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
      return false;
    }

    // Prevent consecutive spaces
    if (
      inputValue.length > 0 &&
      inputValue.endsWith(' ') &&
      event.code === 'Space'
    ) {
      event.preventDefault();
      return false;
    }

    const keyCode: number = event.charCode || event.keyCode;
    if (
      (keyCode >= 65 && keyCode <= 90) || // A-Z
      (keyCode >= 97 && keyCode <= 122) || // a-z
      keyCode === 8 || // Backspace
      keyCode === 32 // Space - now allowed
    ) {
      return true; // Allow the keypress
    } else {
      event.preventDefault();
      return false; // Block the keypress
    }
  }

  funRestNumber(event: any) {
    var k = event.charCode || event.keyCode; // For older browser compatibility

    // Allow numbers (key codes 48 to 57) and the Backspace key (key code 8)
    if ((k >= 48 && k <= 57) || k == 8) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }

  funRestSpace(event: any) {
    if (event.code === 'Space') {
      event.preventDefault();
    }
    var k;
    k = event.charCode;
    if (k >= 48 && k <= 57) {
    } else if (event.key == ' ') {
    } else {
    }

    return k >= 48 && k <= 57;
  }

  funGSTValidation(event: KeyboardEvent): void {
    const inputChar = String.fromCharCode(event.charCode);
    const allowedChars = /^[0-9A-Z]+$/; // Allow digits (0-9) and uppercase letters (A-Z)
    const currentValue: string = (event.target as HTMLInputElement).value + inputChar;

    if (currentValue.length > 15 || !allowedChars.test(inputChar)) {
      event.preventDefault();
    }
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  customEmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const email = control.value as string;
      if (email) {
        const isGmail = email.includes('@');
        const isCom = email.includes('.com');
        if (isGmail && isCom) {
          return null;
        }
      }
      return { invalidEmail: true };
    };
  }

  phoneNumberValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const phone = control.value as string;

      if (phone) {
        if (/^[1-5]/.test(phone.charAt(0))) {
          return { phoneNumberInvalid: true };
        }
      }

      return null;
    };
  }

  onPreviewShow(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    let sidebarValue = this.sidebarService.getBooleanValue();
    this.rewardPointService._openedPopup.emit(true);
    this.previewDialogRef = this.dialog.open(this.bulkPreviewBox, {
      disableClose: false,
      panelClass: 'preview-dialog-container',
      data: data,
      hasBackdrop: true,
    });
    this.previewDialogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  filterDropdown(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    let sidebarValue = this.sidebarService.getBooleanValue();
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogRef = this.dialog.open(this.filterMenuDailog, {
      width: '17%',
      height: '195px',
      position: {
        top: '14%',
        right: '4%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'confirm-dialog-container',
      data: data,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
    // }
  }

  exportUserData(data?: any) {
  }

  UplEmployeeFilterDropdown(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    let sidebarValue = this.sidebarService.getBooleanValue();
    this.rewardPointService._openedPopup.emit(true);
    this.uplEmployeeFilterDialogRef = this.dialog.open(
      this.uplEmployeeFilterMenuDailog,
      {
        width: '16%',
        height: '190px',
        position: {
          top: '14%',
          right: '4%',
        },
        backdropClass: 'custom-backdrop',
        panelClass: 'confirm-dialog-container',
        data: data,
        disableClose: false,
        hasBackdrop: true,
      }
    );
    this.uplEmployeeFilterDialogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  downloadTemplate() {
    this.spinner.show();
    const exports: Observable<Blob> =
      this.userService.downloadTemplateBulkUpload();
    exports.subscribe({
      next: (exportsData: Blob) => {
        this.saveDataAsExcel(
          exportsData,
          'Samridhi_Users_Bulk_Upload_Template'
        );
        this.spinner.hide();
      },
      error: (errorResponse: any) => {
        this.spinner.hide();
      },
    });
  }

  saveDataAsExcel(data: Blob, fileName: string): void {
    // const excelData = new Blob([data], {
    //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    // });
    // saveAs(excelData, fileName + '.xlsx');
  }

  submitBulkUploadFile(event: any) {
    this.spinner.show();
    let uploadFileObject = {
      file: this.formData,
    };
    this.userService.bulkUploadFileBulkUpload(uploadFileObject).subscribe({
      next: (bulkUpload: any) => {
        bulkUpload = JSON.parse((bulkUpload));
        this.toastr.success(bulkUpload.message);
        this.spinner.hide();
        this.clearFileInput();
      },
      error: (errorResponse: any) => {
        errorResponse = JSON.parse((errorResponse.error));
        this.spinner.hide();
        this.toastr.error(errorResponse.message);
        this.clearFileInput();
      },
    });
  }

  clearFileInput() {
    this.previewDialogRef.close();
    this.isPreview = false;
    this.isSubmitDisabled = true;
    this.drawRoot.textContent = '';
  }
  //upload
  getFilesByType(type: string): { type: string, name: string }[] {
    return this.uploadedFiles.filter(f => f.type === type);
  }
  onFileSelected1(event: any, fileType: string) {
    const file = event.target.files[0];
    if (!file) return;

    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg'];

    // ✅ Size check
    if (file.size > maxSize) {
      this.toastr.error(`File size should not exceed 10MB`);
      event.target.value = '';
      return;
    }

    // ✅ MIME type check
    if (!allowedTypes.includes(file.type)) {
      this.toastr.error(`Please upload a valid PDF, JPEG, or PNG file`);
      event.target.value = '';
      return;
    }

    // ✅ Read first 4KB of file to detect suspicious patterns (XSS/JS)
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const content = e.target.result as string;

      const suspiciousPattern = /<script|javascript:|onerror=|<iframe|\/JavaScript|\/Launch|\/OpenAction|\/JS/i;
      if (suspiciousPattern.test(content)) {
        this.toastr.error('Invalid PDF');
        event.target.value = '';
        return;
      }

      // ✅ Push to uploadedFiles if safe
      this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);
      this.uploadedFiles.push({
        type: fileType,
        name: file.name,
        file: file
      });
      event.target.value = '';
    };

    // Read the first 4KB only to keep it lightweight
    reader.readAsText(file.slice(0, 4096));
  }


  // Clear all selected data
  clearFields() {
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedState = [];
    this.selectedCity = [];
    this.selectedCrop = [];

    this.regionValue = [];
    this.zoneValue = [];
    this.stateValue = [];
    this.cityValue = [];
    this.cropValue = [];

    this.uploadedFiles = [];

    // Clear child contact fields
    this.loginUsers = [];

    // Reset form-related fields (but keep data lists intact)
    this.enableMultipleLogin = false;
    this.disableMultipleLoginToggle = false;
    this.companyId = null;
    this.addressId = null;

    // Note: We don't clear regionDataList, zoneDataList, etc.
    // because those contain the dropdown options from API
  }

  // Clear all data for fresh add popup (including data lists)
  clearAllFieldsForAdd() {
    // Clear all selections
    this.clearFields();

    // Also clear data lists for add mode to ensure cascading works properly
    this.zoneDataList = [];
    this.stateDataList = [];
    this.cityDataList = [];
    this.cropDataList = [];

    // Keep regionDataList as it's loaded initially
    // this.regionDataList should remain intact
  }

  // ================== REGION ==================
  selectRegion(event: any) {
    this.selectedRegion = event;

    // Clear all below Region
    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    // Clear state, city, crop as well
    this.selectedState = [];
    this.stateDataList = [];
    this.stateValue = '';

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.selectedCrop = [];
    this.cropDataList = [];
    this.cropValue = '';

    this.getZoneByID(this.selectedRegion.id); // Fetch zones for selected region
  }

  deselectionRegion(event: any) {

    this.selectedRegion = [];

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    this.selectedState = [];
    this.stateDataList = [];
    this.stateValue = '';

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.selectedCrop = [];
    this.cropDataList = [];
    this.cropValue = '';
  }

  deselectionAllRegion(event: any) {
    this.deselectionRegion(event);

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    this.selectedState = [];
    this.stateDataList = [];
    this.stateValue = '';

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.selectedCrop = [];
    this.cropDataList = [];
    this.cropValue = '';
  }

  // ================== ZONE ==================
  selectZone(event: any) {
    this.selectedZone = event;

    // Clear all below Zone
    this.selectedState = [];
    this.stateDataList = [];
    this.stateValue = '';

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.selectedCrop = [];
    this.cropDataList = [];
    this.cropValue = '';
    
    this.getCropByID(this.selectedZone.id);   // Fetch crops for selected zone
    this.getStateByID(this.selectedZone.id); // Fetch states under selected zone
  }

  deselectionZone(event: any) {
    this.selectedZone = [];

    this.selectedState = [];
    this.stateDataList = [];
    this.stateValue = '';

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.selectedCrop = [];
    this.cropDataList = [];
    this.cropValue = '';
  }

  deselectionAllZone(event: any) {
    this.deselectionZone(event);
  }

  // ================== STATE ==================
  selectState(event: any) {
    this.selectedState = event;

    // Clear all below State
    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';

    this.getCityByID(this.selectedState.id); // Fetch cities under selected state
  }

  deselectionState(event: any) {
    this.selectedState = [];

    this.selectedCity = [];
    this.cityDataList = [];
    this.cityValue = '';
  }

  deselectionAllState(event: any) {
    this.deselectionState(event);
  }

  // ================== CITY ==================
  selectCity(event: any) {
    this.selectedCity = event;
  }

  deselectionCity(event: any) {
    this.selectedCity = [];
  }

  deselectionAllCity(event: any) {
    this.deselectionCity(event);
  }

  // ================== CROP ==================
  selectCrop(event: any) {
    this.selectedCrop = event;
  }

  deselectionCrop(event: any) {
    this.selectedCrop = [];
  }

  deselectionAllCrop(event: any) {
    this.deselectionCrop(event);
  }
  getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.regionDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
      }
    );
  }

  getStateByID(id: any) {
    const data = {
      stateId: id
    }; // if needed, add request params here
    this.stateDataList = [];
    this.userService.getStateByID(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.stateDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        // console.error('❌ Failed to fetch redeemed method data', error);
      }
    );
  }

  getZoneByID(id: any) {
    const data = {
      zoneId: id
    }; // if needed, add request params here
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.zoneDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        // console.error('❌ Failed to fetch redeemed method data', error);
      }
    );
  }
  getCityByID(id: any) {
    const data = {
      cityId: id
    }; // if needed, add request params here
    this.cityDataList = [];
    this.userService.getCityById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.cityDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        // console.error('❌ Failed to fetch redeemed method data', error);
      }
    );
  }
  getCropByID(id: any) {
    const data = {
      zoneId: id
    }; // if needed, add request params here
    this.cropDataList = []
    this.userService.getCropById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.cropDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        // console.error('❌ Failed to fetch redeemed method data', error);
      }
    );
  }


  uploadAgreement(data?: any) {
    this.agreementId = data.id
    this.getAllAgreement(data.id)
    this.uploadAggrementDialogRef = this.dialog.open(this.uploadAggrement, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    this.uploadAggrementDialogRef.afterClosed().subscribe(() => {
      // Clean up if needed
    });
  }

  getAgreementStatus() {
    this.agreementDataList = [];
    this.userService.getAgreementStatus().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response);
        let leaderArray = JSON.parse(response);
        this.agreementDataList = leaderArray.map((item: any) => ({
          id: item,
          name: item,
        }));
      },
      (error) => {
        console.error(' Failed to fetch agreementStatus', error);
      }
    );
  }
  getAllAgreement(id: any) {
    this.userService.getAllAgreement(id).subscribe({
      next: (response: any) => {
        response = this.utility.decryptString(response);
        const data = JSON.parse(response);
        this.uploadTableData = data.content.map((item: any) => ({
          uploadagreement: item.agreementFileName || '---',
          startDate: item.startDate || "--",
          endDate: item.endDate || "--",
          uploadedBy: item.uploadedByCustomerName || '--',
          uploadedOn: item.createdDate || "--",
          status: item.status || "--",
          url: item.agreementUrl,
          agreementId: item.id
        }));

        this.isAgreementActive = this.uploadTableData.some((item: any) => item.status === 'ACTIVE');
      },
      error: (errorResponse: any) => {
        errorResponse = this.utility.decryptString(errorResponse)
        errorResponse = (errorResponse.error);
        errorResponse = JSON.parse(errorResponse);
        this.toastr.error(errorResponse.message);
      },
    });
  }


  openAgreement() {
    // this.uploadedFiles = [];
    // this.getAllAgreement(data.id)
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth(); // 0-indexed (0 = Jan, 2 = March, 3 = April)

    const financialYearStart = currentMonth < 3 ? currentYear - 1 : currentYear;
    const financialYearEnd = currentMonth < 3 ? currentYear : currentYear + 1;

    this.startDate = new Date(financialYearStart, 3, 1); // April 1st
    this.endDate = new Date(financialYearEnd, 2, 31);    // March 31st

    // Update the form with these dates
    this.targetForm.patchValue({
      startDate: this.startDate,
      endDate: this.endDate
    });
    this.dialog.closeAll();
    this.uploadAggrementFileDialogRef = this.dialog.open(this.uploadAggrementFile, {
      width: '30%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    this.uploadAggrementFileDialogRef.afterClosed().subscribe(() => {
      this.uploadedFiles = [];
    });
  }

  onFileSelect(event: any, fileType: string) {
    const file = event.target.files[0];
    if (!file) return;

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toastr.error('File size should not exceed 10MB');
      event.target.value = '';
      return;
    }

    const isValidType = fileType === 'pdf' && file.type === 'application/pdf';
    if (!isValidType) {
      this.toastr.error('Only PDF files are allowed');
      event.target.value = '';
      return;
    }

    // Overwrite old file of this type
    this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);
    this.uploadedFiles.push({ type: fileType, name: file.name, file });

    event.target.value = '';
  }


  onCancel(): void {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    const financialYearStart = currentMonth < 3 ? currentYear - 1 : currentYear;
    const financialYearEnd = currentMonth < 3 ? currentYear : currentYear + 1;

    this.startDate = new Date(financialYearStart, 3, 1); // April 1st
    this.endDate = new Date(financialYearEnd, 2, 31);    // March 31st

    this.uploadedFiles = [];
    this.uploadAggrementFileDialogRef.close();

    this.uploadAggrementDialogRef = this.dialog.open(this.uploadAggrement, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });
  }


  onSubmited(): void {
    this.spinner.show();
    const startDate = this.targetForm.value.startDate;
    const endDate = this.targetForm.value.endDate;

    if (!this.uploadedFiles?.length || !this.agreementId || !startDate || !endDate) {
      this.spinner.hide();
      this.toastr.warning("Please fill all required fields and upload a valid PDF.");
      return;
    }

    const pdfFileObj = this.uploadedFiles.find(f => f.type === 'pdf');
    if (!pdfFileObj || !pdfFileObj.file) {
      this.spinner.hide();
      this.toastr.error('Please upload a PDF file.');
      return;
    }

    const file = pdfFileObj.file;
    const maxFileSizeMB = 10;
    if (file.size > maxFileSizeMB * 1024 * 1024) {
      this.spinner.hide();
      this.toastr.error(`File size should not exceed ${maxFileSizeMB}MB.`);
      return;
    }

    const reader = new FileReader();
    reader.onload = (event: any) => {
      const content = event.target.result as string;

      const suspiciousPatterns = /\/(JavaScript|JS|AA|OpenAction|Launch|EmbeddedFile)/i;

      if (suspiciousPatterns.test(content)) {
        this.spinner.hide();
        this.toastr.error("The uploaded PDF contains potentially unsafe content.");
        return;
      }

      // If safe, proceed
      const formattedStartDate = new Date(startDate).toISOString().split('T')[0];
      const formattedEndDate = new Date(endDate).toISOString().split('T')[0];

      const formData = new FormData();
      formData.append('file', file);

      const payload = {
        userId: this.agreementId,
        startDate: formattedStartDate,
        endDate: formattedEndDate
      };

      this.userService.uploadAgreement(formData, payload).subscribe({
        next: (response: any) => {
          this.toastr.success("Agreement uploaded successfully");
          this.uploadAggrementFileDialogRef.close();
          this.leaderData();
          this.spinner.hide();
        },
        error: (err: any) => {
          const decrypted = this.utility.decryptString(err.error);
          const errorObj = JSON.parse(decrypted);
          const message = errorObj?.message || 'Invalid PDF';
          this.toastr.error(message);
          this.spinner.hide();
        }
      });
    };

    // Read first 4KB of the PDF to check for embedded JS
    reader.readAsText(file.slice(0, 4096));
  }


  deleteRowData(data: any) {
    let message = 'Are you sure you want to delete the Agreement ?'
    this.openDeleteDialog(message, data)
  }

  openDeleteDialog(message: string, data: any): void {
    this.dialog.closeAll();
    const dialogRef = this.dialog.open(DeletePopupComponent, {
      data: { message, data },
      width: '350px',
      height: "250px",
      disableClose: false,
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.spinner.show();
        this.userService.deleteAgreementById(data.agreementId).subscribe({
          next: (response: any) => {
            this.toastr.success("Agreement deleted successfully.");
            this.leaderData();
            this.spinner.hide();

          },
          error: (err: any) => {
            console.error('Error deleting agreement:', err);
            this.spinner.hide();
          }
        });
      } else {
        this.uploadAggrementDialogRef = this.dialog.open(this.uploadAggrement, {
          width: '60%',
          disableClose: false,
          panelClass: 'custom-popup',
          hasBackdrop: true,
        });
      }
    });

  }
  onClose() {
    this.uploadAggrementDialogRef.close();
  }
  dateRangeValidator(): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const startDate = group.get('startDate')?.value;
      const endDate = group.get('endDate')?.value;

      if (!startDate || !endDate) {
        return null;
      }

      const errors: any = {};

      if (startDate.getTime() === endDate.getTime()) {
        errors.datesAreSame = true;
      }

      if (endDate < startDate) {
        errors.endDateBeforeStartDate = true;
      }

      return Object.keys(errors).length ? errors : null;
    };
  }


  downloadAgreementPdf(data: any) {
    if (!data || !data.uploadagreement) {
      this.toastr.warning('No agreement file available to download');
      return;
    }

    this.spinner.show();

    const fileName = data.uploadagreement; // Use the file name as-is (with spaces)
    const fileURL = data.url;

    this.userService.downloadDocumentFiles(fileURL).subscribe({
      next: (response: Blob) => {
        const blob = new Blob([response], { type: 'application/pdf' }); // or use correct MIME if dynamic
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading agreement:', error);
        this.toastr.error('Failed to download agreement file');
      },
      complete: () => {
        this.spinner.hide();
      }
    });
  }







  onDownload(url: string, filename: string) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.target = '_blank';
    link.click();
  }

  checkFormValidity(): boolean {
    // Check if we're in edit mode
    const isEditMode = !!this.userId;

    // Check form validity
    const isFormValid = this.validateFormFields();

    // Check if all required selections are made
    const selectionsValid = this.validateSelections(isEditMode);
    const documentsUploaded = isEditMode || this.validateDocuments();
    return isFormValid && selectionsValid && documentsUploaded;
  }

  private validateFormFields(): boolean {
    const baseValid = this.appUserForm.valid &&
      !!this.appUserForm.get('firstName')?.value?.trim() &&
      !!this.appUserForm.get('lastName')?.value?.trim() &&
      !!this.appUserForm.get('mobileNumber')?.value?.trim() &&
      this.appUserForm.get('mobileNumber')?.value?.trim().length === 10 &&
      !!this.appUserForm.get('email')?.value?.trim() &&
      !!this.appUserForm.get('address')?.value?.trim() &&
      !!this.appUserForm.get('companyname')?.value?.trim();

    if (!baseValid) return false;

    // ✅ If multiple login enabled, validate each user row
    if (this.enableMultipleLogin) {
      for (const user of this.loginUsers) {
        if (
          !user.multiFirstName?.trim() ||
          !user.multiLastName?.trim() ||
          !user.phoneNumber?.trim() ||
          user.phoneNumber.trim().length !== 10 ||
          !user.email?.trim()
        ) {
          return false;
        }
      }
    }

    return true;
  }


  private validateSelections(isEditMode: boolean): boolean {
    // Helper function to check if a selection is valid
    const isSelectionValid = (editValue: any, addValue: any): boolean => {
      const value = isEditMode ? editValue : addValue;
      return value && (Array.isArray(value)
        ? value.length > 0
        : (Array.isArray(value[0]) ? value[0].length > 0 : !!value.id || !!value[0]?.id));
    };

    // Check each selection
    const hasRegion = isSelectionValid(this.regionValue, this.selectedRegion);
    const hasZone = isSelectionValid(this.zoneValue, this.selectedZone);
    const hasState = isSelectionValid(this.stateValue, this.selectedState);
    const hasCity = isSelectionValid(this.cityValue, this.selectedCity);
    // const hasCrop = isSelectionValid(this.cropValue, this.selectedCrop);

    return hasRegion && hasZone && hasState && hasCity
  }

  private validateDocuments(): boolean {
    return this.getFilesByType('rfcDoc').length > 0 &&
      this.getFilesByType('idDoc').length > 0;
  }

  addLoginUser() {
    this.loginUsers.push({
      multiFirstName: '',
      multiLastName: '',
      phoneNumber: '',
      email: '',
      fromApi: false,
      countryCode: 'MX'
    });
  }

  isLastRowFilled(): boolean {
    if (!this.loginUsers.length) return false;
    const lastUser = this.loginUsers[this.loginUsers.length - 1];
    return (
      lastUser.multiFirstName.trim() !== '' &&
      lastUser.multiLastName.trim() !== '' &&
      lastUser.phoneNumber.trim().length === 10 &&
      lastUser.email.trim() !== ''
    );
  }

  removeLoginUser(index: number) {
    this.loginUsers.splice(index, 1);
  }

  onMultipleLoginToggle() {
    if (!this.enableMultipleLogin) {
      this.loginUsers = [];
    } else {
      if (!this.loginUsers.length) {
        this.addLoginUser();
      }
    }
  }
}
