import { Component, TemplateRef, ViewChild, OnInit, ViewEncapsulation } from '@angular/core';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { BaThemeSpinner } from 'src/app/theme/services/baThemeSpinner/baThemeSpinner.service';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { AppConstant } from '../../constants/app.constant';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { CommonModule } from '@angular/common';
import { ApproverManagementService } from 'src/app/app-services/approver-management.service';
import { UserService } from 'src/app/app-services/user-service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { DeletePopupComponent } from 'src/app/modals/delete-popup/delete-popup.component';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { Utility } from 'src/app/shared/utility/utility';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  changeStatus: boolean;
  showStatus: boolean;
}

interface TableData {
  selected: boolean;
  productName: string;
  quantity: number;
  unitCost: number;
  amount: number;
  searchBy: string;
  statusName: any[];
  dropdownSettings: any;
}

interface Role {
  id: number;
  name: string;
  role: any;
}

@Component({
  selector: 'app-approver-management',
  imports: [
    DynamicTableComponent,
    AngularMultiSelectModule,
    ReactiveFormsModule,
    FormsModule,
    MatTableModule,
    MatCheckboxModule,
    MatSelectModule,
    MatFormFieldModule,
    CommonModule
  ],
  templateUrl: './approver-management.component.html',
  styleUrls: ['./approver-management.component.scss'],
  standalone: true,
  encapsulation: ViewEncapsulation.None
})
export class ApproverManagementComponent implements OnInit {

  onDataDeselectAll($event: any[], _t145: any) {
    throw new Error('Method not implemented.');
  }
  userRole: string = '';
  isAdmin: boolean = false;
  activityName: any;
  activityList: any[] = [];
  approverManagementService: ApproverManagementService;
  editingId: number | null = null;

  constructor(
    private spinner: BaThemeSpinner,
    private events: GlobalEvents,
    public dialog: MatDialog,
    private approverService: ApproverManagementService,
    private userService: UserService,
    private router: Router,
    private toastr: ToastrService,
    private utility: Utility
  ) {
    this.spinner.hide();
    this.events.setChangedContentTopText('Create Approval Flow');
    this.approverManagementService = approverService;
  }

  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showStatus: true,
    showEdit: true,
    productIcon: false,
    changeStatus: false
  };

  statusDropdownLeaderName = {
    text: '',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'itemName',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  baseDropdownSettings = {
    text: '',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'itemName',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  tableHead: any[] = [];
  tableColName: any[] = [];
  approverListData: any[] = [];
  showIndex: any = { index: null };
  @ViewChild('createLevel') createLevelTemplate!: TemplateRef<any>;
  createLevelDialogRef!: MatDialogRef<any>;
  statusDataList: any[] = [];
  statusLeaderName: any[] = [];
  selectedStatusLeader: any = [];
  DataList: any[] = [];
  selectedData: any = [];
  showLevel2: boolean = false;
  showLevel3: boolean = false;

  displayedColumns: string[] = ['productName', 'quantity', 'unitCost', 'amount', 'searchBy'];
  dataSource = new MatTableDataSource<TableData>([]);

activityDropdownSettings = {
  text: 'Select Activity',
  enableSearchFilter: true,
  classes: 'myclass customer-dd',
  labelKey: 'showName',     // 👈 show this in dropdown
  primaryKey: 'name',       // 👈 store this as the value
  enableFilterSelectAll: false,
  singleSelection: true,
  maxHeight: 150,
  disabled: false,
  autoPosition: false,
  badgeShowLimit: 1,
  showCheckbox: false,
  noDataLabel: 'No activities found',
  searchPlaceholderText: 'Search activities',
  clearSearchFilter: true
};

  approvalLevels = [1, 2, 3, 4];
  showLevel = [true, false, false, false];
  levelRoles: any = [[], [], [],[]];

  // NEW: Method to handle level deletion with shifting
  deleteLevel(index: number) {
    if (index === 1) {
      // If deleting level 2
      if (this.showLevel[2]) {
        // Move level 3 to level 2
        this.levelRoles[1] = [...this.levelRoles[2]];
        this.levelRoles[2] = [];
        this.showLevel[1] = true;
        this.showLevel[2] = false;
      } else {
        // Just hide level 2
        this.showLevel[1] = false;
        this.levelRoles[1] = [];
      }
    } else if (index === 2) {
      // For level 3 deletion
      this.showLevel[2] = false;
      this.levelRoles[2] = [];
    }
  }

  onActivitySelect(event: any) {
    const selectedItem = this.activityList.find(item => item.name === event.name);
    this.activityName = selectedItem;
  }

  onActivityDeselect(event: any) {
    this.activityName = null;
  }

  onActivityDeselectAll(event: any) {
    this.activityName = null;
  }

  ngOnInit() {
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
    });
    
    this.events.setChangedContentTopText('Create Approval Flow');
    this.tableHead = [
      'Activity',
      'Level 1',
      'Level 2',
      'Level 3',
      'Level 4',
      'Created By',
      'Created On',
    ];
    this.tableColName = [
      'activityName',
      'level1',
      'level2',
      'level3',
      'level4',
      'uploadedByCustomerName',
      'createdDate',
    ];
    this.dataSource.data = [
      { selected: true, productName: 'ITEM-XYZ123', quantity: 10, unitCost: 50, amount: 500, searchBy: '[47, "Aj Aj"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
      { selected: false, productName: 'ITEM-XYZ123', quantity: 20, unitCost: 75, amount: 1500, searchBy: '', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: true } },
      { selected: false, productName: 'ITEM-XYZ123', quantity: 15, unitCost: 60, amount: 900, searchBy: '', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: true } },
      { selected: true, productName: 'ITEM-XYZ123', quantity: 25, unitCost: 80, amount: 2000, searchBy: '[45, "Aryan Giri"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
      { selected: true, productName: 'ITEM-XYZ123', quantity: 30, unitCost: 90, amount: 2700, searchBy: '[6, "Luisa Ramírez"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
    ];
    this.getAllRedeemedMethodData();
    this.getActivityNames();
    this.getAllRoles();
  }

  private setRoleBasedConfig(currentRole: string) {
    this.isAdmin = currentRole === 'ADMIN';
    this.userRole = currentRole;
    this.configurationSettings.showActionsColumn = this.isAdmin;
  }
  
  getPageData(page: any) {
    // Handle pagination if needed
  }

  openAddSlabDialog(data?: any) {
    // Reset form state
    this.resetForm();

    this.createLevelDialogRef = this.dialog.open(this.createLevelTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    // Small delay to ensure DOM is rendered before multiselect initialization
    setTimeout(() => {
      // Force change detection after dialog is rendered
      if (this.createLevelDialogRef) {
        // Dialog is open and ready
      }
    }, 100);

    this.createLevelDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.close();
    });
  }
  
  openDeleteDialog(message: string, data: any): void {
    const dialogRef = this.dialog.open(DeletePopupComponent, {
      data: { message, data },
      width: '350px',
      disableClose: false
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {    
        this.spinner.show();
        const id = this.utility.encrypt(JSON.stringify(data.id))
        this.approverService.deleteApprovalLevel(data.id).subscribe({
          next: (response: any) => {
            const decrypted = this.utility.decryptString(response); 
            // decrypt response string
            this.toastr.success(decrypted || 'Deleted successfully');
            this.getAllRedeemedMethodData();
            this.spinner.hide();
          },
          error: (err: any) => {
            this.toastr.error(err.error.message || 'Error occurred');
            this.spinner.hide();
          }
        });
        
      } else {
        this.spinner.hide();
        console.log('Cancelled - do nothing or show message');
      }
    });
  }
   
  close() {
    if (this.createLevelDialogRef) {
      this.createLevelDialogRef.close();
    }
    this.resetForm();
  }

  resetForm() {
    this.editingId = null;
    this.activityName = [];
    this.showLevel = [true, false, false, false];
    this.levelRoles = [[], [], [], []];

    // Ensure arrays are properly initialized
    for (let i = 0; i < 4; i++) {
      if (!this.levelRoles[i]) {
        this.levelRoles[i] = [];
      }
    }
  }

  submit() {
    this.spinner.show();
    const requestPayload: any = {
      activityName: this.activityName[0]?.name || ''
    };
    
    if (this.editingId) {
      requestPayload.id = this.editingId;
    }
    
    for (let i = 0; i < this.approvalLevels.length; i++) {
      if ((i === 0 || this.showLevel[i]) && this.levelRoles[i]?.length > 0) {
        const levelKey = `level${i + 1}`;
        requestPayload[levelKey] = this.levelRoles[i][0]?.role || '';
      }
    }
    
    const apiCall = this.editingId 
      ? this.approverManagementService.updateApprovalLevel(requestPayload)
      : this.approverManagementService.createApprovalLevel(requestPayload);
    
    apiCall.subscribe({
      next: (response: any) => {
        this.spinner.hide();
        this.toastr.success(this.editingId ? 'Approval level updated successfully' : 'Approval level created successfully');
        this.close();
        this.getAllRedeemedMethodData();
        this.editingId = null;
      },
      error: (error: any) => {
        this.spinner.hide();
        let errorMessage = this.editingId ? 'Failed to update approval level' : 'Failed to create approval level';
        
        try {
          if (typeof error.error === 'string') {
            const parsedError = JSON.parse(error.error);
            errorMessage = parsedError.message || errorMessage;
          } else if (error.error && error.error.message) {
            errorMessage = error.error.message;
          }
        } catch (e) {
          console.error('Error parsing error response', e);
        }
        
        if (errorMessage === 'Full authentication is required to access this resource') {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(errorMessage);
        }
      }
    });
  }
  
  onCheckboxChange(row: TableData) {
    row.selected = !row.selected;
    row.dropdownSettings = { ...row.dropdownSettings, disabled: !row.selected };
    if (!row.selected) {
      row.statusName = [];
    }
  }

  onDataSelect(event: any, row: TableData) {
    row.statusName = [event];
  }

  onDataDeselect(event: any, row: TableData) {
    row.statusName = [];
  }

  getAllRedeemedMethodData() {
    const data = {};
    this.approverService.getApproverData(data).subscribe({
      next: (response: any) => {
        try {
          // First, check if response is a string and parse it if needed
          let parsedResponse = response;
          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }
          
          // Check if response has encryptedBody property
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            const rawData = decrypted ? JSON.parse(decrypted) : JSON.parse(response);
                        this.approverListData = rawData.map((item: any) => ({
              ...item,
              activityName: this.utility.formatActivityName(item.activityName)
            }));
          } else {
            // Try to parse the response directly
           const rawData = JSON.parse(response);
                        this.approverListData = rawData.map((item: any) => ({
              ...item,
              activityName: this.utility.formatActivityName(item.activityName)
            }));          }
        } catch (error) {
          console.error('Error parsing approver data:', error);
          this.approverListData = [];
        }
      },
      error: (error: any) => {
        console.error('Failed to fetch approver data:', error);
        this.approverListData = [];
      }
    });
  }

  deleteRowData (data: any){
    let message = 'Are you sure you want to delete the Approval level ?'
    this.openDeleteDialog(message, data)
  }
  
  setPreSelectedValues() {
    this.dataSource.data.forEach(row => {
      if (row.searchBy) {
        try {
          const match = row.searchBy.match(/\[(\d+),\s*"([^"]+)"\]/);
          if (match) {
            const id = parseInt(match[1], 10);
            const itemName = match[2];
            const matchingItem = this.DataList.find(item => item.id === id && item.itemName === itemName);
            if (matchingItem) {
              row.statusName = [matchingItem];
            }
          }
        } catch (error) {
          console.error('Error parsing searchBy for row:', row, error);
          row.statusName = [];
        }
      } else {
        row.statusName = [];
      }
      row.dropdownSettings = { ...this.baseDropdownSettings, disabled: !row.selected };
    });
  }

getActivityNames() {
  this.approverManagementService.getActivityList().subscribe({
    next: (response: any) => {
      try {
        let parsedResponse = response;
        if (typeof response === 'string') {
          parsedResponse = JSON.parse(response);
        }

        if (parsedResponse && parsedResponse.encryptedBody) {
          const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
          const activityData = JSON.parse(decrypted);
          this.activityList = activityData.map((activity: any) => ({
            id: activity.id,
            name: activity.name,
            showName: activity.name
              ?.split('_')
              .map((word: string) => word.toUpperCase())
              .join(' ')
          }));
        } else {
          const activityData = typeof response === 'string' ? JSON.parse(response) : response;
          this.activityList = activityData.map((activity: any) => ({
            id: activity.id,
            name: activity.name,
            showName: activity.name
              ?.split('_')
              .map((word: string) => word.toUpperCase())
              .join(' ')
          }));
        }
      } catch (error) {
        console.error('Error parsing activity list:', error);
        this.activityList = [];
      }
    },
    error: (error: any) => {
      console.error('Failed to fetch activity list:', error);
      this.activityList = [];
    }
  });
}

  roleList: any[] = [];
  roleDropdownSettings = {
    text: 'Select Role',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  getAllRoles() {
    this.userService.getAllCustomerRole().subscribe({
      next: (roles: any) => {
        roles=this.utility.decryptString(roles)
        try {
          // First, check if response is a string and parse it if needed
          let parsedRoles = roles;
          if (typeof roles === 'string') {
            parsedRoles = JSON.parse(roles);
          }
          
          // Check if response has encryptedBody property
          if (parsedRoles && parsedRoles.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedRoles.encryptedBody);
            parsedRoles = JSON.parse(decrypted);
          }
          
          this.roleList = [];
          if (Array.isArray(parsedRoles)) {
            parsedRoles.forEach((roleInfo: any, index: number) => {
              const roleName = roleInfo || '';

              const roleObj = {
                id: index + 1,
                name: roleName
                  ? roleName.charAt(0).toUpperCase() + roleName.slice(1).toLowerCase()
                  : '',
                role: roleInfo
              };
              this.roleList.push(roleObj);
            });
          } else {
            console.warn("Roles is not an array", parsedRoles);
          }
        } catch (e) {
          console.error("Invalid JSON in response", e);
        }
      },
      error: (errorResponse: any) => {
        let error: any = errorResponse.error;

        try {
          error = JSON.parse(error);
        } catch (e) {
          console.error("Error parsing error response", e);
        }

        if (error.message === 'Full authentication is required to access this resource') {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        } else {
          this.toastr.error(error.message || 'An error occurred while fetching roles');
        }
      },
    });
  }

  editApprovalLevel(data: any) {
    this.activityName = [];
    this.showLevel = [true, false, false, false];
    this.levelRoles = [[], [], [], []];
    this.editingId = data.id;
    
    if (data.activityName) {
      // Match using showName property since data.activityName comes as 'ADD PROMOTION'
      // but activityList has showName as 'ADD PROMOTION' and name as 'ADD_PROMOTION'
      const activityItem = this.activityList.find(item => item.showName === data.activityName);
      if (activityItem) {
        this.activityName = [activityItem];
      }
    }
    
    // Initialize levels with shifting support
    let lastVisibleLevel = 0;
    for (let i = 0; i < this.approvalLevels.length; i++) {
      const levelKey = `level${i + 1}`;
      if (data[levelKey]) {
        // Only show level if it has a value
        this.showLevel[i] = true;
        lastVisibleLevel = i;
        
        const roleItem = this.roleList.find(role => role.role === data[levelKey]);
        if (roleItem) {
          this.levelRoles[i] = [roleItem];
        }
      } else {
        // Hide subsequent levels if no value
        this.showLevel[i] = false;
      }
    }
    
    // Ensure levels are consecutive
    for (let i = 0; i <= lastVisibleLevel; i++) {
      this.showLevel[i] = true;
    }
    
    this.createLevelDialogRef = this.dialog.open(this.createLevelTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    // Small delay to ensure DOM is rendered and data is populated
    setTimeout(() => {
      // Force change detection after dialog is rendered and data is set
      if (this.createLevelDialogRef) {
        // Dialog is open and data is populated
      }
    }, 150);

    this.createLevelDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.close();
    });
  }
}
