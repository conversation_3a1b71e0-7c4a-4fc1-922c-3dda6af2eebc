<!-- Updated template section with level shifting -->
<div class="app-container">
  <div class="app-grid-container">
    <div class="app-grid-data-parent-container">
      <div id="foo" class="app-grid-data-container">
        <div class="history-filter-container">
          <div class="left-column second-left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <span class="table-heading"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="isAdmin"  class="right-column">
            <div class="input-group">
              <div class="add-btn-container">
                <button class="create-level-btn" title="Add" (click)="openAddSlabDialog($event)">
                  <img width="18px" src="../../../../assets/img/addButton.png" alt="Add Button" />
                  <span>Create Level</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="app-table">
          <dynamic-table [tableHeads]="tableHead" [tableData]="approverListData"
            [tableConfiguration]="configurationSettings" [tableColName]="tableColName"
            (deleteRow)="deleteRowData($event)"
            (onRowEdit)="editApprovalLevel($event)">
          </dynamic-table>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #createLevel>
  <div class="popup-overlay">
    <div class="popup-container">
      <div class="popup-header">
        <h3>{{ editingId ? 'Edit level' : 'Create level' }}</h3>
      </div>
      <div class="form-container">
        <div class="form-group with-icon">
          <label for="activity">Activity Name<i class="required">*</i></label>
          <span class="dropdown-icon-level">
            <angular2-multiselect
              [data]="activityList"
              [(ngModel)]="activityName"
              [settings]="activityDropdownSettings"
              [ngModelOptions]="{standalone: true}">
            </angular2-multiselect>
          </span>
        </div>

        <div *ngFor="let level of approvalLevels; let i = index">
          <div [ngClass]="{ 'form-group': true, 'with-icon': true }" *ngIf="i === 0 || showLevel[i]">
            <label>Level {{ i + 1 }}<i class="required" *ngIf="i === 0">*</i></label>
            <div [ngClass]="{ 'dropdown-icon-level': i === 0, 'dropdown-icon': i > 0 }">
              <angular2-multiselect
                [data]="roleList"
                [(ngModel)]="levelRoles[i]"
                [settings]="roleDropdownSettings"
                [ngModelOptions]="{standalone: true}">
              </angular2-multiselect>

              <!-- Level 1 controls -->
              <ng-container *ngIf="i === 0">
                <button type="button" class="add-btn" (click)="showLevel[1] = true" *ngIf="!showLevel[1]">
                  <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
              </ng-container>

              <!-- Level 2 controls -->
              <ng-container *ngIf="i === 1">
                <div class="button-group-inline">
                  <button type="button" class="delete-btn" (click)="deleteLevel(i)">
                    <i class="fa fa-trash" aria-hidden="true"></i>
                  </button>
                  <button type="button" class="add-btn" (click)="showLevel[2] = true" *ngIf="!showLevel[2]">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                  </button>
                </div>
              </ng-container>

              <!-- Level 3 controls -->
              <ng-container *ngIf="i === 2">
                <div class="button-group-inline">
                  <button type="button" class="delete-btn" (click)="deleteLevel(i)">
                    <i class="fa fa-trash" aria-hidden="true"></i>
                  </button>
                  <button type="button" class="add-btn" (click)="showLevel[3] = true" *ngIf="!showLevel[3]">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                  </button>
                </div>
              </ng-container>

              <!-- Level 4 controls -->
              <ng-container *ngIf="i === 3">
                <button type="button" class="delete-btn" (click)="deleteLevel(i)">
                  <i class="fa fa-trash" aria-hidden="true"></i>
                </button>
              </ng-container>
            </div>
          </div>
        </div>

        <div class="button-group">
          <button type="button" class="cancel-btn" (click)="close()">Cancel</button>
          <button type="button" class="create-btn"
            [disabled]="!activityName?.length || !levelRoles[0]?.length"
            [ngClass]="{ 'disable-submit': !activityName?.length || !levelRoles[0]?.length }"
            (click)="submit()">
            {{ editingId ? 'Update' : 'Create' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
