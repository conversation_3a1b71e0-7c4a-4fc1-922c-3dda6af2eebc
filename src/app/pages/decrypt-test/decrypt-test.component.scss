.decrypt-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .test-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;

      h2 {
        margin: 0 0 10px 0;
        font-size: 28px;
        font-weight: 600;
      }

      p {
        margin: 0;
        opacity: 0.9;
        font-size: 16px;
      }
    }

    .card-content {
      padding: 30px;
    }
  }

  .error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;

    &.success-message {
      background: #efe;
      border-color: #cfc;
      color: #3c3;
    }
  }

  .input-section,
  .output-section {
    margin-bottom: 25px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      label {
        font-weight: 600;
        color: #333;
        font-size: 16px;
      }

      .button-group-header {
        display: flex;
        gap: 8px;
      }

      .copy-btn,
      .clean-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: #5a6268;
          transform: translateY(-1px);
        }

        &:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }

      .clean-btn {
        background: #17a2b8;

        &:hover:not(:disabled) {
          background: #138496;
        }
      }
    }

    .input-textarea,
    .output-textarea {
      width: 100%;
      padding: 15px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
      resize: vertical;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &::placeholder {
        color: #adb5bd;
        font-style: italic;
      }
    }

    .input-textarea {
      background: #f8f9fa;
    }

    .output-textarea {
      background: #fff;
      border-color: #28a745;
    }
  }

  .action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 30px 0;
    flex-wrap: wrap;

    button {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 120px;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }
    }

    .decrypt-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #218838, #1ea080);
      }
    }

    .encrypt-btn {
      background: linear-gradient(135deg, #007bff, #6610f2);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #0056b3, #520dc2);
      }
    }

    .clear-btn {
      background: linear-gradient(135deg, #dc3545, #e83e8c);
      color: white;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #c82333, #d91a72);
      }
    }
  }

  .instructions {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 20px;
    border-radius: 0 8px 8px 0;
    margin-top: 30px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 18px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
        color: #555;

        strong {
          color: #333;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 10px;

    .test-card .card-content {
      padding: 20px;
    }

    .action-buttons {
      flex-direction: column;
      align-items: center;

      button {
        width: 100%;
        max-width: 200px;
      }
    }

    .input-section .section-header,
    .output-section .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
}
