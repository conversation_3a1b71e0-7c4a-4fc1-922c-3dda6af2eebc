import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Utility } from '../../shared/utility/utility';
import { BaThemeSpinner } from 'src/app/theme/services';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-decrypt-test',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './decrypt-test.component.html',
  styleUrls: ['./decrypt-test.component.scss']
})
export class DecryptTestComponent implements OnInit {
  
  encryptedInput: string = '';
  decryptedOutput: string = '';
  errorMessage: string = '';
  isLoading: boolean = false;

  constructor(
    private utility: Utility,
    private spinner: BaThemeSpinner,
    private router: Router
  ) {
    this.spinner.hide();
  }

  ngOnInit() {
    this.checkProductionAccess();
  }

  private checkProductionAccess() {
    // Check if we're in production environment
    const currentUrl = window.location.origin;
    const productionUrl = 'https://pgmlider.upl-ltd.com';

    // Only block the exact production URL, allow dev and other environments
    if (currentUrl === productionUrl) {
      console.log('Production environment detected. Redirecting...');

      // Clear any stored authentication data
      localStorage.clear();
      sessionStorage.clear();

      // Redirect to login page
      this.router.navigate(['/sign-in']).then(() => {
        // Show alert to user
        alert('This testing tool is not available in production environment.');
      });

      return;
    }

    // Additional check using environment configuration if available
    if (environment.production && environment.baseUrl &&
        environment.baseUrl === productionUrl) {
      console.log('Production environment detected via config. Redirecting...');

      // Clear any stored authentication data
      localStorage.clear();
      sessionStorage.clear();

      // Redirect to login page
      this.router.navigate(['/sign-in']).then(() => {
        alert('This testing tool is not available in production environment.');
      });
    }
  }

  onDecrypt() {
    if (!this.encryptedInput.trim()) {
      this.errorMessage = 'Please enter an encrypted string';
      this.decryptedOutput = '';
      return;
    }

    this.isLoading = true;
    this.spinner.show();
    this.errorMessage = '';
    this.decryptedOutput = '';

    try {
      // Clean the input - remove quotes and extract only the encrypted string
      let cleanedInput = this.cleanEncryptedString(this.encryptedInput.trim());
      console.log("Original input:", this.encryptedInput);
      console.log("Cleaned input:", cleanedInput);

      // Attempt to decrypt the cleaned input
      const decrypted = this.utility.decrypt(cleanedInput);
      console.log("Decrypted result:", decrypted);

      try {
        // Try to parse as JSON for better formatting
        const parsedJson = JSON.parse(decrypted);
        console.log('Parsed JSON:', parsedJson);
        this.decryptedOutput = JSON.stringify(parsedJson, null, 2);
      } catch (jsonError) {
        // If not valid JSON, just show the decrypted string
        this.decryptedOutput = decrypted;
      }

      this.errorMessage = '';
    } catch (error: any) {
      this.errorMessage = `Decryption failed: ${error.message || 'Invalid encrypted string'}`;
      this.decryptedOutput = '';
      console.error("Decryption error:", error);
    } finally {
      this.isLoading = false;
      this.spinner.hide();
    }
  }

  // Helper method to clean encrypted string from quotes and extra characters
  cleanEncryptedString(input: string): string {
    // Remove surrounding quotes (single or double)
    let cleaned = input.replace(/^["']|["']$/g, '');

    // If the input looks like a JSON response, try to extract the encrypted value
    if (cleaned.startsWith('{') || cleaned.startsWith('[')) {
      try {
        const parsed = JSON.parse(cleaned);
        // Look for common property names that might contain the encrypted string
        if (parsed.encryptedBody) {
          cleaned = parsed.encryptedBody;
        } else if (parsed.data) {
          cleaned = parsed.data;
        } else if (parsed.response) {
          cleaned = parsed.response;
        } else if (typeof parsed === 'string') {
          cleaned = parsed;
        }
      } catch (e) {
        // If JSON parsing fails, continue with the original cleaned string
        console.log("JSON parsing failed, using original string");
      }
    }

    // Remove any remaining quotes
    cleaned = cleaned.replace(/^["']|["']$/g, '');

    // Remove any whitespace
    cleaned = cleaned.trim();

    return cleaned;
  }

  onEncrypt() {
    if (!this.decryptedOutput.trim()) {
      this.errorMessage = 'Please enter text to encrypt in the output field';
      this.encryptedInput = '';
      return;
    }

    this.isLoading = true;
    this.spinner.show();
    this.errorMessage = '';

    try {
      // Encrypt the output text
      const encrypted = this.utility.encrypt(this.decryptedOutput.trim());
      this.encryptedInput = encrypted;
      this.errorMessage = '';
      console.log("Encrypted result:", encrypted);
    } catch (error: any) {
      this.errorMessage = `Encryption failed: ${error.message || 'Invalid input'}`;
      this.encryptedInput = '';
      console.error("Encryption error:", error);
    } finally {
      this.isLoading = false;
      this.spinner.hide();
    }
  }

  cleanInput() {
    if (!this.encryptedInput.trim()) {
      this.errorMessage = 'No input to clean';
      return;
    }

    try {
      const cleaned = this.cleanEncryptedString(this.encryptedInput.trim());
      this.encryptedInput = cleaned;
      this.errorMessage = cleaned !== this.encryptedInput.trim() ?
        'Input cleaned - quotes and extra characters removed' :
        'Input was already clean';

      // Clear error message after 3 seconds
      setTimeout(() => {
        if (this.errorMessage.includes('cleaned') || this.errorMessage.includes('already clean')) {
          this.errorMessage = '';
        }
      }, 3000);
    } catch (error: any) {
      this.errorMessage = `Failed to clean input: ${error.message}`;
    }
  }

  clearAll() {
    this.encryptedInput = '';
    this.decryptedOutput = '';
    this.errorMessage = '';
  }

  copyToClipboard(text: string, type: string) {
    if (!text) {
      this.errorMessage = `No ${type} text to copy`;
      return;
    }

    navigator.clipboard.writeText(text).then(() => {
      // Temporarily show success message
      const originalError = this.errorMessage;
      this.errorMessage = `${type} copied to clipboard!`;
      setTimeout(() => {
        this.errorMessage = originalError;
      }, 2000);
    }).catch(() => {
      this.errorMessage = 'Failed to copy to clipboard';
    });
  }
}
