<div class="decrypt-test-container">
  <div class="test-card">
    <div class="card-header">
      <h2>🔐 Encryption/Decryption Testing Tool</h2>
      <p>Test encryption and decryption functionality</p>
    </div>

    <div class="card-content">
      <!-- Error Message -->
      <div *ngIf="errorMessage" class="error-message" [ngClass]="{'success-message': errorMessage.includes('copied')}">
        {{ errorMessage }}
      </div>

      <!-- Encrypted Input Section -->
      <div class="input-section">
        <div class="section-header">
          <label for="encryptedInput">Encrypted String Input:</label>
          <div class="button-group-header">
            <button
              type="button"
              class="clean-btn"
              (click)="cleanInput()"
              [disabled]="!encryptedInput.trim()">
              🧹 Clean
            </button>
            <button
              type="button"
              class="copy-btn"
              (click)="copyToClipboard(encryptedInput, 'Encrypted text')"
              [disabled]="!encryptedInput">
              📋 Copy
            </button>
          </div>
        </div>
        <textarea
          id="encryptedInput"
          [(ngModel)]="encryptedInput"
          placeholder="Paste your encrypted string here..."
          rows="4"
          class="input-textarea">
        </textarea>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button 
          type="button" 
          class="decrypt-btn" 
          (click)="onDecrypt()"
          [disabled]="!encryptedInput.trim() || isLoading">
          <span *ngIf="isLoading">🔄 Processing...</span>
          <span *ngIf="!isLoading">🔓 Decrypt</span>
        </button>
        
        <button 
          type="button" 
          class="clear-btn" 
          (click)="clearAll()"
          [disabled]="isLoading">
          🗑️ Clear All
        </button>
      </div>

      <!-- Decrypted Output Section -->
      <div class="output-section">
        <div class="section-header">
          <label for="decryptedOutput">Decrypted JSON Output:</label>
          <button 
            type="button" 
            class="copy-btn" 
            (click)="copyToClipboard(decryptedOutput, 'Decrypted text')"
            [disabled]="!decryptedOutput">
            📋 Copy
          </button>
        </div>
        <textarea
          id="decryptedOutput"
          [(ngModel)]="decryptedOutput"
          placeholder="Decrypted result will appear here..."
          rows="12"
          class="output-textarea">
        </textarea>
      </div>

      <!-- Instructions -->
      <div class="instructions">
        <h4>📝 Instructions:</h4>
        <ul>
          <li><strong>To Decrypt:</strong> Paste encrypted string in the top field and click "Decrypt"</li>
          <li><strong>To Clean Input:</strong> Click "🧹 Clean" to automatically remove quotes and extract encrypted string from responses like <code>"kSFgSyy0WzYA4EsKQsUAxvGSSrBwoebFp+sweep0sWBGbyFeOeBdACtdUfL+Pfuv"</code></li>
          <li><strong>To Encrypt:</strong> Enter/edit text in the bottom field and click "Encrypt"</li>
          <li><strong>Copy:</strong> Use copy buttons to copy text to clipboard</li>
          <li><strong>JSON:</strong> If decrypted text is valid JSON, it will be formatted automatically</li>
        </ul>
      </div>
    </div>
  </div>
</div>
