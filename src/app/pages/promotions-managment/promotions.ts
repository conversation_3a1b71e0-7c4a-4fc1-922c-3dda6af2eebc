import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PageEvent } from '@angular/material/paginator';
import { Router, RouterModule } from '@angular/router';
import { ngxCsv } from 'ngx-csv';
import { ToastrService } from 'ngx-toastr';
import { Subject, debounceTime } from 'rxjs';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';

import { SupportService } from 'src/app/app-services/support.service';
import { AppConstant } from 'src/app/constants/app.constant';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { Utility } from "src/app/shared/utility/utility";
import { BaThemeSpinner } from 'src/app/theme/services';
import { CommonModule } from '@angular/common';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { UserService } from 'src/app/app-services/user-service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { promotionsService } from 'src/app/app-services/promotions.service';
import moment from 'moment';
import { NumberFormatService } from 'src/app/shared/shared/number-format.service';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  showApprove?: boolean;
  showDelete?: boolean;
  changeStatus: boolean;
  isApprove?: boolean;
  isRejected?: boolean;
}

export enum ToggleEnum {
  Option1,
  Option2,
}
@Component({
  selector: 'app-all-promotions',
  templateUrl: './promotions.html',
  styleUrls: ['./promotions.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatPaginatorModule,
    MatDialogModule,
    AngularMultiSelectModule,
    DynamicTableComponent,
  ]
})

export class AllPromotionsComponent implements OnInit {
  viewProductsDialogRef!: MatDialogRef<any>;
  @ViewChild('viewProductsTemplate') viewProductsTemplate!: TemplateRef<any>;
  @ViewChild('supoortDialog') supoortDialog: TemplateRef<any> | undefined;
  @ViewChild('addEditPromotionTemplate') addEditPromotionTemplate!: TemplateRef<any>;
  @ViewChild('approvePromotionTemplate') approvePromotionTemplate!: TemplateRef<any>;
  @ViewChild('rejectPromotionTemplate') rejectPromotionTemplate!: TemplateRef<any>;
  supoortDialogRef!: MatDialogRef<any>;
  ismobileViewAllBrands: boolean = false;
  isAdmin: boolean = false;
  formGroup: FormGroup = new FormGroup({
    comments: new FormControl('', Validators.required)
  });

  dataTable: boolean = false;
  isView: boolean = false;
  isViewFalse: boolean = false;
  newFlag: boolean = false;
  progressFlag: boolean = false;
  userData: any = [];
  tableHead: any = [];
  tableColName: any = [];
  tableData: any = [];
  exportData: any = [];
  modelChanged: Subject<string> = new Subject<string>();
  showIndex: any = { index: null };

  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Action',
    productIcon: true,
    noDataMessage: 'No data found',
    showEdit: true,
    showApprove: true,
    showDelete: false, // Disable the old delete functionality
    changeStatus: false,
    isApprove: true, // Add this property for approve buttons
    isRejected: true, // Add this property for reject buttons
  };

  promotionTab: string = 'PROMOTIONS';
  selectedStatus: string = '';
  activeButton: string | undefined;
  selectedDate: string | undefined;
  selectedValue: string = '1';
  isNewTicket: boolean = true;
  isprogressTicket: boolean = false;
  model: string = '';
  searchedValue: string | undefined;
  startDate: any;
  endDate: any;

  ticketNumber: string | undefined;
  queryTitle: string | undefined;
  createdDate: Date | undefined;

  totalRecordCount = 0;
  perPage = AppConstant.PER_PAGE_ITEMS;
  currentPage = 0;

  // Separate counts for each tab
  promotionsCount = 0;
  progressCount = 0;

  productRows: any[] = [];
  deletedProductIds: any[] = []; // Store deleted product IDs for edit mode
  categoryDataList: any = [];
  dialogCategoryDataList: any = [];
  category: any = [];
  dialogCategory: any = [];
  statusDataList: any = [];
  status: any = [];
  ticketId: number | undefined;

  selectedCategory: string = '';
  selectedDialogCategory: string = '';

  statusDropdownSettings = {
    text: 'Select Status',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  promotionAttachments: any = [];
  userRole: string | null | undefined;
  viewProductHead: any = [];
  viewProductData: any = [];
  viewProductColName: any = [];

  previewConfigurationSettings: ConfigurationSettings = {
    showPagination: false,
    perPage: 0,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: '',
    productIcon: false,
    noDataMessage: 'No products found',
    showEdit: false,
    changeStatus: false,
  };

  promotionForm !: FormGroup;
  isEditMode: boolean = false;
  editingPromotionId: number | null = null;
  isProcessingSubmit: boolean = false;
  selectedFileName: string = '';
  selectedFile: File | null = null;
  uploadedImageUrl: string = '';
  uploadedImageId: number | null = null;
  isImageUploading: boolean = false;
  additionalProducts: any[] = [];
  addEditPromotionDialogRef!: MatDialogRef<any>;

  selectedRegion: any[] = [];
  selectedZone: any[] = [];
  selectedProduct: any[] = [];
  selectedDistributor: any[] = [];

  regionDataList = [];
  zoneDataList: any[] = [];
  distributorDataList: any[] = [];

  productDataList: any[] = [];
  DataList: any[] = [];

  regionDropdownSettings = {
    singleSelection: false, // Allow multiple selection for regions
    text: "Select Regions",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 3,
    searchPlaceholderText: 'Search Region',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: true
  };

  zoneDropdownSettings = {
    singleSelection: false, // Allow multiple selection for zones
    text: "Select Zones",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 3,
    searchPlaceholderText: 'Search Zone',
    labelKey: 'itemName',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: true
  };

  distributorDropdownSettings = {
    singleSelection: false, // Allow multiple selection for distributors
    text: "Select Distributors",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 3,
    searchPlaceholderText: 'Search Distributors',
    labelKey: 'itemName',
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    showCheckbox: true
  };

  productDropdownSettings = {
    singleSelection: true,
    text: "Select Product",
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    enableSearchFilter: true,
    classes: "multiselect-custom",
    badgeShowLimit: 1,
    searchPlaceholderText: 'Search Product',
    labelKey: 'itemName',
    maxHeight: 130,
    disabled: false,
    autoPosition: false,
    showCheckbox: false
  };

  // Approve/Reject promotion properties
  selectedPromotionData: any = null;
  rejectComment: string = '';
  approvePromotionDialogRef!: MatDialogRef<any>;
  rejectPromotionDialogRef!: MatDialogRef<any>;
  constructor(
    private spinner: BaThemeSpinner,
    private supportService: SupportService,
    private toastr: ToastrService,
    private router: Router,
    private events: GlobalEvents,
    private utility: Utility,
    public dialog: MatDialog,
    private userService: UserService,
    private rewardPointService: RewardPointsService,
    private formBuilder: FormBuilder,
    private promotionsService: promotionsService,
    private cdr: ChangeDetectorRef,
    private numberFormatService: NumberFormatService,
  ) {
    this.ismobileViewAllBrands = window.innerWidth <= 1023 ? true : false;
    this.initializePromotionForm();
  }

  ngOnInit() {
    this.spinner.show();
    this.initializePromotionForm();
    this.getRegion();
    this.loadDistributorData();
    this.loadAllProducts();
    this.isAdmin = parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this.spinner.hide();
    }
    this.userService.fetchUserRole();

    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      this.userRole = apiRoleFormatted;
      this.isAdmin = apiRoleFormatted === 'ADMIN' || parseInt(AuthenticationHelper.getRoleID() ?? "") === 1;

      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this.spinner.hide();
      }
    });
    this.setTableHeader();
    this.modelChanged.pipe(debounceTime(500)).subscribe((model: string) => {
      this.searchedValue = model?.trim() || '';
      this.currentPage = 0;
      this.configurationSettings.currentPage = 1;
      this.setTableHeader();
    });
  }
  private setRoleBasedConfig(role: string) {
    if (role === 'VIEWER') {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: false
      };
      setTimeout(() => {
        this.configurationSettings = { ...this.configurationSettings };
        this.tableData = [...(this.tableData || [])];
        this.setTableHeader();
      }, 100);
    }
    else {
      this.configurationSettings = {
        ...this.configurationSettings,
        showActionsColumn: true
      };
    }
  }


  toggleEnum = ToggleEnum;
  selectedState = ToggleEnum.Option1;
  onChange($event: any) {
    this.selectedState = $event.value;
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.perPage = event.pageSize;
    if (this.isNewTicket) {
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.getProgress();
    }
  }
  /**
   * Method for routing to edit or add user
   * @param event
   */
  id: any;
  title: any;
  description: string | undefined;

  userDetails(event: any) {
    let data = {
      id: event.id,
      ticketNumber: event.ticketNumber,
      title: event.title,
      description: event.description,
      comment: event.comment,
    };
    this.supoortDialogRef = this.dialog.open(this.supoortDialog!, {
      width: '70%',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      data,
      hasBackdrop: true,
    });
  }


  initializePromotionForm() {
    this.promotionForm = this.formBuilder.group({
      promotionName: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      points: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(1)]]

    });
  }

  // Multiselect event handlers
  onRegionSelect(item: any): void {
    // Load zones for all selected regions
    this.loadZonesForSelectedRegions();
    this.triggerFormValidation();
  }

  onRegionDeSelect(item: any): void {
    // When a region is deselected, remove zones that belong to that region
    this.removeZonesForDeselectedRegion(item);
    this.loadZonesForSelectedRegions();
    this.triggerFormValidation();
  }

  onRegionSelectAll(event: any): void {
    this.loadZonesForSelectedRegions();
    this.triggerFormValidation();
  }

  onRegionDeSelectAll(event: any): void {
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = [];
    this.cdr.detectChanges();
    this.triggerFormValidation();
  }

  onZoneSelect(item: any): void {
    // Refresh dropdown to ensure it stays responsive
    this.refreshZoneDropdown();
    this.triggerFormValidation();
  }

  onZoneDeSelect(item: any): void {
    // Refresh dropdown to ensure it stays responsive
    this.refreshZoneDropdown();
    this.triggerFormValidation();
  }

  onZoneSelectAll(event: any): void {
    // Refresh dropdown to ensure it stays responsive
    this.refreshZoneDropdown();
    this.triggerFormValidation();
  }

  onZoneDeSelectAll(event: any): void {
    this.selectedZone = [];
    // Refresh dropdown state after clearing selections
    this.refreshZoneDropdown();
    this.triggerFormValidation();
  }

  // Method to handle zone dropdown open event
  onZoneDropdownOpen(): void {
    // Ensure zone data is available when dropdown is opened
    if (this.selectedRegion && this.selectedRegion.length > 0 &&
        (!this.zoneDataList || this.zoneDataList.length === 0)) {
      this.loadZonesForSelectedRegions();
    }
  }

  // Distributor event handlers
  onDistributorSelect(item: any): void {
    this.triggerFormValidation();
  }

  onDistributorDeSelect(item: any): void {
    this.triggerFormValidation();
  }

  onDistributorSelectAll(event: any): void {
    this.triggerFormValidation();
  }

  onDistributorDeSelectAll(event: any): void {
    this.selectedDistributor = [];
    this.triggerFormValidation();
  }

  onProductSelect(item: any): void {
    this.selectedProduct = [item];
  }

  onProductDeSelect(item: any): void {
    this.selectedProduct = [];
  }

  onAdditionalProductSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [item];
    this.additionalProducts[index].productId = item.id;
    this.triggerFormValidation();
  }

  onAdditionalProductDeSelect(item: any, index: number): void {
    this.additionalProducts[index].selectedProduct = [];
    this.additionalProducts[index].productId = null;
    this.triggerFormValidation();
  }

  getRegion() {
    if (this.regionDataList.length > 0) {
      return;
    }

    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.regionDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
        }));
        this.cdr.detectChanges();
      },
      (error) => {
      }
    );
  }

  loadDistributorData() {
    this.promotionsService.getAllDistributors().subscribe({
      next: (response: any) => {
        try {
          response = this.utility.decryptString(response);
          console.log('response', response);
          let distributorData;

          if (typeof response === 'string' && response) {
            try {
              distributorData = JSON.parse(response);
            } catch (e) {
              distributorData = JSON.parse(response);
            }
          } else {
            distributorData = response;
          }
          console.log('distributorData', distributorData);
          this.distributorDataList = distributorData.map((distributor: any) => ({
            id: distributor.id,
            itemName: distributor.distributorName,
            code: distributor.distributorCode
          }));

          console.log('Mapped distributorDataList:', this.distributorDataList);

          // Force change detection to update dropdown
          this.cdr.detectChanges();

          this.refreshSelectedDistributorsWithLoadedData();
        } catch (error: any) {
          this.handleDecryptionError("Failed to load distributor data", error);
        }
      },
      error: (errorResponse: any) => {
        this.handleApiError(errorResponse);
      }
    });
  }

  refreshSelectedDistributorsWithLoadedData() {
    if (this.selectedDistributor && this.selectedDistributor.length > 0 && this.distributorDataList.length > 0) {
      const updatedSelection: any[] = [];

      this.selectedDistributor.forEach((selected: any) => {
        const matchingDistributor = this.distributorDataList.find(distributor =>
          distributor.id == selected.id
        );

        if (matchingDistributor) {
          updatedSelection.push(matchingDistributor);
        } else {
          updatedSelection.push(selected);
        }
      });

      this.selectedDistributor = updatedSelection;
    }
  }

  loadZonesForSelectedRegions() {
    if (!this.selectedRegion || this.selectedRegion.length === 0) {
      this.zoneDataList = [];
      this.selectedZone = [];
      // Force change detection when clearing zones
      this.cdr.detectChanges();
      return;
    }

    // Store currently selected zones to preserve them
    const currentlySelectedZones = [...this.selectedZone];

    // Clear existing zones
    this.zoneDataList = [];

    // Load zones for each selected region
    let loadedRegions = 0;
    const totalRegions = this.selectedRegion.length;

    this.selectedRegion.forEach(region => {
      const data = { zoneId: region.id };

      this.userService.getZoneById(data).subscribe(
        (response: any) => {
          try {
            response = this.utility.decryptString(response);
            let zoneArray = JSON.parse(response);

            // Add zones from this region to the list (avoid duplicates)
            zoneArray.forEach((zone: any) => {
              const existingZone = this.zoneDataList.find(z => z.id === zone.id);
              if (!existingZone) {
                this.zoneDataList.push({
                  id: zone.id,
                  itemName: zone.name,
                  code: zone.code,
                  regionId: region.id // Track which region this zone belongs to
                });
              }
            });

            loadedRegions++;
            if (loadedRegions === totalRegions) {
              // Restore previously selected zones that are still valid
              this.restoreValidSelectedZones(currentlySelectedZones);

              // Force change detection when all zones are loaded
              this.cdr.detectChanges();

              // Additional delay to ensure dropdown is properly refreshed
              setTimeout(() => {
                this.cdr.detectChanges();
              }, 100);
            }
          } catch (error) {
            console.error('Error parsing zone data:', error);
            loadedRegions++;
            if (loadedRegions === totalRegions) {
              this.restoreValidSelectedZones(currentlySelectedZones);
              this.cdr.detectChanges();
            }
          }
        },
        (error) => {
          console.error('Error loading zones for region:', region.id, error);
          loadedRegions++;
          if (loadedRegions === totalRegions) {
            this.restoreValidSelectedZones(currentlySelectedZones);
            this.cdr.detectChanges();
          }
        }
      );
    });
  }

  // Helper method to restore valid selected zones
  private restoreValidSelectedZones(previouslySelectedZones: any[]) {
    if (!previouslySelectedZones || previouslySelectedZones.length === 0) {
      return;
    }

    const validSelectedZones: any[] = [];

    previouslySelectedZones.forEach(selectedZone => {
      const matchingZone = this.zoneDataList.find(zone =>
        zone.id === selectedZone.id ||
        (zone.itemName === selectedZone.itemName && zone.regionId === selectedZone.regionId)
      );

      if (matchingZone) {
        validSelectedZones.push(matchingZone);
      }
    });

    this.selectedZone = validSelectedZones;
  }

  removeZonesForDeselectedRegion(deselectedRegion: any) {
    // Remove zones that belong to the deselected region
    this.zoneDataList = this.zoneDataList.filter(zone => zone.regionId !== deselectedRegion.id);

    // Remove selected zones that belong to the deselected region
    this.selectedZone = this.selectedZone.filter(zone => zone.regionId !== deselectedRegion.id);

    // Force change detection to update dropdown state
    this.cdr.detectChanges();

    // Additional delay to ensure dropdown is properly refreshed
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 50);
  }

  // Method to refresh zone dropdown state
  refreshZoneDropdown() {
    // Only refresh if we have zone data to work with
    if (this.zoneDataList && this.zoneDataList.length > 0) {
      // Create new array references to force dropdown refresh
      this.zoneDataList = [...this.zoneDataList];
      this.selectedZone = [...this.selectedZone];

      // Force change detection
      this.cdr.detectChanges();

      // Additional delay for dropdown component to fully refresh
      setTimeout(() => {
        this.cdr.detectChanges();
      }, 100);
    } else if (this.selectedRegion && this.selectedRegion.length > 0) {
      // If no zone data but regions are selected, reload zones
      this.loadZonesForSelectedRegions();
    }
  }

  getZoneByID(id: any) {
    const data = {
      zoneId: id
    };
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        response = this.utility.decryptString(response)
        let leaderArray = JSON.parse(response);
        this.zoneDataList = leaderArray.map((item: any) => ({
          id: item.id,
          itemName: item.name,
          code: item.code,
          name: item.name
        }));

        // Force change detection after zones are loaded
        this.cdr.detectChanges();

        // Additional delay to ensure dropdown is refreshed
        setTimeout(() => {
          this.cdr.detectChanges();
        }, 100);
      },
      (error) => {
      }
    );
  }

  getAllProductData(event: any) {
    this.spinner.show();
    this.rewardPointService.getProductByID({ id: event.id }).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));

          this.setPreSelectedValues(); // Update preselections after data load
        }
        this.spinner.hide();
      },
      error: (error) => {
        this.toastr.error('Failed to load products');
        this.spinner.hide();
      }
    });
  }

  setPreSelectedValues() {
    // Method to handle pre-selected values if needed
    // This can be implemented based on your requirements
  }

  async openDialogForm(data: any) {
    // Open the edit promotion dialog instead of the old promotion dialog
    this.openEditPromotionDialog(data);
  }



  async waitForTicketInfo(data: any): Promise<void> {
    return new Promise<void>((resolve) => {
      this.setTicketInfo(data);
      resolve(); // Resolve the promise immediately since setTicketInfo doesn't return a promise
    });
  }

  setTicketInfo(ticketInfo: any) {
    this.promotionAttachments = [];
    ticketInfo.promotionAttachments.forEach((promotionAttachment: any) => {
      let promotionAttachmentObject = {
        id: promotionAttachment.id,
        url: promotionAttachment.url,
        fileName: promotionAttachment.fileName,
        contentType: promotionAttachment.contentType,
        fileSize: promotionAttachment.fileSize,
        promotionMediaType: promotionAttachment.promotionMediaType,
      };
      this.promotionAttachments.push(promotionAttachmentObject);
    });
    this.dialogCategory = [];
    this.status = [];
    this.dialogCategoryDataList.forEach((categoryData: any) => {
      if (categoryData.name == ticketInfo.category) {
        this.dialogCategory.push(categoryData);
        this.selectedDialogCategory = categoryData.name;
      }
    });
    this.statusDataList.forEach((statusData: any) => {
      if (statusData.status == ticketInfo.status) {
        this.status.push(statusData);
        this.selectedStatus = statusData.status;
      }
    });
    this.ticketId = ticketInfo.id;
    if (this.isNewTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber],
        queryTitle: [ticketInfo.title],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate)],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    } else if (this.isprogressTicket) {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.updatedDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
        distributorName: [ticketInfo.distributorName, Validators.required],
        distributorCode: [ticketInfo.distributorCode, Validators.required],
      });
    } else {
      this.formGroup = this.formBuilder.group({
        ticketNumber: [ticketInfo.ticketNumber, Validators.required],
        queryTitle: [ticketInfo.title, Validators.required],
        leaderName: [ticketInfo.leaderName, Validators.required],
        createdDate: [new Date(ticketInfo.createdDate), Validators.required],
        comments: [ticketInfo.comment, Validators.required],
        description: [ticketInfo.description, Validators.required],
      });
    }
    this.formGroup.patchValue({
      comments: ''
    });
    this.formGroup.get('ticketNumber')?.disable();
    this.formGroup.get('queryTitle')?.disable();
    this.formGroup.get('description')?.disable();
  }

  /**
   * Triggered when category are selected
   * @param data
   */
  onCloseForm() {
    this.supoortDialogRef.close();
    this.formGroup.reset();
    this.selectedCategory = '';
    this.selectedDialogCategory = '';
    this.selectedStatus = '';
    this.categoryDataList = [];
    this.statusDataList = [];
    this.category = [];
    this.status = [];
    this.promotionAttachments = [];
    this.formGroup.patchValue({
      comments: '',
    });
  }



  /**
   * Method for the page change event
   * @param page
   */
  getUsersPageData(page: any) {
    // this.currentPage = page;
    if (!this.newFlag) {
      this.getProgress(this.currentPage);
    } else {
      this.getPromotionData(this.currentPage);
    }
  }

  getpromotionPageData(page: number): void {
    this.configurationSettings.currentPage = page;
    this.currentPage = page - 1; // Convert to 0-based index for API
    if (this.isNewTicket) {
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.getProgress();
    }
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    if (this.isNewTicket) {
      this.tableHead = [
        'Promotion Name',
        'Products',
        'Distributor Name',
        'Region',
        'Zone',
        'Start Date',
        'End Date',
        'Points Multiplier',
        'Status',
      ];
      this.tableColName = [
        'promotionName',
        'products',
        'distributorName',
        'region',
        'zone',
        'startDate',
        'endDate',
        'points',
        'status',
      ];
      this.tableData = [];
      this.getPromotionData();
    } else if (this.isprogressTicket) {
      this.tableHead = [
        'Promotion Name',
        'Leader Name',
        'Product Name',
        'Portfolio',
        'Category',
        'Total Quantity',
        'Achieved Quantity',
        'Bonification Amount (Mex$)',
        'Distributor Name',
        'Distributor Code',
      ];
      this.tableColName = [
        'promotionName',
        'leaderName',
        'productName',
        'portfolio',
        'category',
        'totalQuantity',
        'achievedQuantity',
        'bonificationAmount',
        'distributorName',
        'distributorCode',
      ];
      this.tableData = [];
      this.getProgress();
    } else {
      this.tableHead = ['Leader Name', 'Ticket No', 'Mobile No', 'Category', 'Query Title', 'Created Date', 'Status'];
      this.tableColName = ['leaderName', 'ticketNumber', 'mobileNo', 'category', 'title', 'createdDate', 'status'];
      this.tableData = [];
      this.getPromotionData();
    }
  }



  /**
   * Method for getting results on the basis of search query
   * @param searchString
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedValue = '';
    this.model = '';
    // Reset pagination when clearing search
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;
    this.setTableHeader();
  }

  /**
   * Method for exporting data in CSV format
   * @param event
   */
  onExport(event: any) {
    if (event) {
      this.getPromotionExportData();
    }
  }


  ticketTab(tab: string) {
    this.spinner.show();
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    this.model = '';
    this.searchedValue = '';
    this.startDate = '';
    this.endDate = '';
    this.category = '';
    this.selectedCategory = '';
    // Reset pagination when switching tabs
    this.currentPage = 0;
    this.configurationSettings.currentPage = 1;

    switch (tab) {
      case 'PROMOTIONS':
        this.promotionTab = 'PROMOTIONS';
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;

      case 'PROGRESS':
        this.promotionTab = 'PROGRESS';
        this.isNewTicket = false;
        this.isprogressTicket = true;
        this.configurationSettings.showActionsColumn = false;  // Always false for PROGRESS tab
        break;

      default:
        this.isNewTicket = true;
        this.isprogressTicket = false;
        if (localRole === 'VIEWER') {
          this.configurationSettings.showActionsColumn = false;
        } else {
          this.configurationSettings.showActionsColumn = true;
        }
        break;
    }
    this.configurationSettings = { ...this.configurationSettings };

    this.setTableHeader();
    this.spinner.hide();
  }
  viewProduct(event: any) {
    this.openViewProduct(event);
  }

  openViewProduct(event: any) {
    this.spinner.show(); // Show spinner before opening dialog

    // Use the promotion's product data from the event

    if (event && event.fullData && event.fullData.products) {
      this.setupProductViewData(event.fullData.products);
      this.openProductViewDialog();
    } else {
      this.toastr.error('No product data available for this promotion');
      this.spinner.hide();
    }
  }

  private openProductViewDialog() {
    this.viewProductsDialogRef = this.dialog.open(this.viewProductsTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.viewProductsDialogRef.afterOpened().subscribe(() => {
      this.spinner.hide(); // Hide spinner after dialog opens
    });

    this.viewProductsDialogRef.backdropClick().subscribe(() => {
      this.viewProductsDialogRef.close();
    });

    this.viewProductsDialogRef.afterClosed().subscribe(() => {
      // Clean up data when dialog closes
      this.viewProductData = [];
    });
  }

  setupProductViewData(promotionProducts?: any[]) {
    // Set up table headers for product view
    this.viewProductHead = [
      'Product Name',
      'Material Code',
      'Portfolio',
      'Category',
      'Target Quantity',
    ];

    this.viewProductColName = [
      'productName',
      'materialCode',
      'portfolio',
      'category',
      'quantity',
    ];

    // Use promotion products if provided, otherwise use DataList
    if (promotionProducts && promotionProducts.length > 0) {
      this.viewProductData = promotionProducts.map((item: any) => ({
        productName: item.productName || 'N/A',
        materialCode: item.materialCode || 'N/A',
        portfolio: item.portfolio || 'N/A',
        category: this.utility.toUpperCaseUtil(item.category ) || 'N/A',
        quantity: item.quantity || 0
      }));
    } else if (this.DataList && this.DataList.length > 0) {
      this.viewProductData = this.DataList.map((item: any) => ({
        productName: item.itemName,
        materialCode: item.materialCode || 'N/A',
        portfolio: item.portfolio || 'N/A',
        category: this.utility.toUpperCaseUtil(item.category) || 'N/A',
        quantity: 0 // Default quantity since this is view data
      }));
    } else {
      // If no data available, show empty array
      this.viewProductData = [];
    }

    this.previewConfigurationSettings.totalRecordCount = this.viewProductData.length;
  }


  closePopup() {
    if (this.viewProductsDialogRef) {
      this.viewProductsDialogRef.close();
    }
  }

  getPageData(page: number) {
    this.previewConfigurationSettings.currentPage = page;
    // In a real application, you would fetch data for the specific page
    // For now, we're using static dummy data
  }

  // Add/Edit Promotion Methods
  openAddPromotionDialog() {

    this.isEditMode = false;
    this.editingPromotionId = null;
    this.promotionForm.reset();
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Reset selections and clear zone data
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = [];
    this.selectedDistributor = [];
    // Don't clear distributorDataList - keep it loaded for the dropdown
    this.deletedProductIds = []; // Clear deleted products

    // Initialize productRows with at least one row
    this.productRows = [{
      selectedProduct: [],
      quantity: null,
      productId: null,
      originalProductData: null
    }];

    // Load all products independently (not dependent on region/zone)
    this.loadAllProducts();

    // Ensure regions are loaded for add dialog
    if (this.regionDataList.length === 0) {
      this.getRegion();
    }

    // Ensure distributors are loaded for add dialog
    if (this.distributorDataList.length === 0) {
      this.loadDistributorData();
    }

    this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
      width: '60%',
      maxWidth: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.resetPromotionDialog();
    });
  }



  openEditPromotionDialog(data: any) {

    this.isEditMode = true;

    // Reset form and clear previous selections
    this.promotionForm.reset();
    this.selectedRegion = [];
    this.selectedZone = [];
    this.productRows = [];
    this.editingPromotionId = null;

    // Clear image data
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Open dialog first
    this.addEditPromotionDialogRef = this.dialog.open(this.addEditPromotionTemplate, {
      width: '60%',
      maxWidth: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false
    });

    // Show spinner inside the popup after dialog opens
    setTimeout(() => {
      this.spinner.show();

      // Load all required data and populate form
      this.loadDataAndPopulateForm(data);
    }, 100); // Small delay to ensure dialog is rendered

    this.addEditPromotionDialogRef.afterClosed().subscribe(() => {
      // Clean up when dialog closes
      this.resetPromotionDialog();
      this.spinner.hide(); // Ensure spinner is hidden if dialog is closed early
    });
  }

  loadDataAndPopulateForm(data: any) {
    let loadedCount = 0;
    const totalLoads = 3; // products, regions, and distributors

    const checkAllLoaded = () => {
      loadedCount++;

      if (loadedCount >= totalLoads) {
        // All data loaded, now populate form

        // Populate form immediately
        this.populateFormForEdit(data);

        // Wait for form population and UI rendering to complete
        setTimeout(() => {
          // Force change detection to ensure all dropdowns are updated
          this.cdr.detectChanges();

          // Wait for all multiselect components to fully render
          setTimeout(() => {
            this.cdr.detectChanges();

            // Final delay to ensure everything is properly bound
            setTimeout(() => {
              this.refreshMultiselectComponents();

              // Force form validation after everything is loaded
              setTimeout(() => {
                this.triggerFormValidation();

                // Hide spinner after final refresh
                setTimeout(() => {
                  this.spinner.hide();
                }, 300);
              }, 500);
            }, 500);
          }, 800); // Wait for zones and products to load
        }, 500);
      }
    };

    // Load products using the same method as add dialog
    this.rewardPointService.getProductByID({}).subscribe({
      next: (response: any) => {
        try {
          const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

          if (Array.isArray(parsedData) && parsedData.length > 0) {
            // Use the same mapping as loadAllProducts method
            this.DataList = parsedData.map((item: any) => ({
              id: item.id || item.databricks_material_cd,
              itemName: item.databricks_material_desc || item.description || item.itemName,
              materialCode: item.databricks_material_cd || '',
              bonusPercentage: item.bonusPercentage || 0,
              materialNumber: item.databricks_material_nbr,
              category: item.category || '',
              portfolio: item.portfolio || '',
            }));

            // Update productDataList for multiselect dropdown
            this.productDataList = this.DataList.map((item: any) => ({
              id: item.id,
              itemName: item.itemName
            }));

          } else {
            this.productDataList = [];
          }

          checkAllLoaded();
        } catch (error) {
          this.productDataList = [];
          checkAllLoaded();
        }
      },
      error: (error: any) => {
        this.productDataList = [];
        checkAllLoaded();
      }
    });

    // Load regions using existing method - but we need to modify it to work with our callback
    this.loadRegionsForEdit(checkAllLoaded);

    // Load distributors for edit dialog
    this.loadDistributorsForEdit(checkAllLoaded);
  }

  loadRegionsForEdit(callback: () => void) {

    // If regions are already loaded, don't reload them
    if (this.regionDataList.length > 0) {
      callback();
      return;
    }

    // Only load if not already loaded
    this.userService.getRegion().subscribe({
      next: (response: any) => {
        try {
          response = this.utility.decryptString(response);
          let leaderArray = JSON.parse(response);
          this.regionDataList = leaderArray.map((item: any) => ({
            id: item.id,
            name: item.name
          }));

          // Force change detection to refresh multiselect
          this.cdr.detectChanges();

          callback();
        } catch (error) {
          callback(); // Still call callback to prevent hanging
        }
      },
      error: (error: any) => {
        callback(); // Still call callback to prevent hanging
      }
    });
  }

  loadDistributorsForEdit(callback: () => void) {
    // If distributors are already loaded, don't reload them
    if (this.distributorDataList.length > 0) {
      callback();
      return;
    }

    // Load distributors using existing method
    this.promotionsService.getAllDistributors().subscribe({
      next: (response: any) => {
        try {
          response = this.utility.decryptString(response);
          const distributorData = JSON.parse(response);

          this.distributorDataList = distributorData.map((distributor: any) => ({
            id: distributor.id,
            itemName: distributor.distributorName || distributor.name || 'Unknown Distributor',
            code: distributor.distributorCode || distributor.code || 'N/A'
          }));

          // Force change detection to refresh multiselect
          this.cdr.detectChanges();
          callback();
        } catch (error) {
          callback(); // Still call callback to prevent hanging
        }
      },
      error: (error: any) => {
        callback(); // Still call callback to prevent hanging
      }
    });
  }

  populateFormForEdit(data: any) {
    const promotionData = data.fullData || data;
    this.editingPromotionId = promotionData.id || null;
    this.promotionForm.patchValue({
      promotionName: promotionData.promotionName || '',
      startDate: promotionData.startDate || '',
      endDate: promotionData.endDate || '',
      points: promotionData.points || ''
    });
    this.populateRegionAndZone(promotionData);
    this.populateDistributors(promotionData);
    this.populateProductRows(promotionData);
    if (promotionData.promotionImages && promotionData.promotionImages.length > 0) {
      const firstImage = promotionData.promotionImages[0];
      this.uploadedImageId = firstImage.imageId || null;
      this.uploadedImageUrl = firstImage.imagePath || '';
      this.selectedFileName = firstImage.imageName || 'Existing Image';
    }

  }

  populateDistributors(promotionData: any) {
    this.selectedDistributor = [];

    if (promotionData.distributorsInvolved && Array.isArray(promotionData.distributorsInvolved)) {
      promotionData.distributorsInvolved.forEach((distributorData: any) => {
        if (this.distributorDataList.length === 0) {
          this.selectedDistributor.push({
            id: distributorData.id,
            itemName: distributorData.distributorName || distributorData.name || distributorData.itemName || 'Unknown Distributor',
            code: distributorData.distributorCode || distributorData.code || 'TEMP' + distributorData.id
          });
        } else {
          const matchingDistributor = this.distributorDataList.find(distributor =>
            distributor.id == distributorData.id
          );
          if (matchingDistributor) {
            this.selectedDistributor.push(matchingDistributor);
          } else {
            this.selectedDistributor.push({
              id: distributorData.id,
              itemName: distributorData.distributorName || distributorData.name || distributorData.itemName || 'Unknown Distributor',
              code: distributorData.distributorCode || distributorData.code || 'TEMP' + distributorData.id
            });
          }
        }
      });
    }
    else if (promotionData.distributorName) {
      const matchingDistributors = this.distributorDataList.filter(distributor =>
        distributor.itemName.toLowerCase().includes(promotionData.distributorName.toLowerCase())
      );

      if (matchingDistributors.length > 0) {
        this.selectedDistributor = matchingDistributors;
      } else {
        this.selectedDistributor = [{
          id: 999,
          itemName: promotionData.distributorName,
          code: 'TEMP999'
        }];
      }
    }
  }

  populateProductRows(promotionData: any) {
    if (!this.productDataList || this.productDataList.length === 0) {
      this.productDataList = [];
      if (promotionData.products && promotionData.products.length > 0) {
        // Only add products with valid productName to the dropdown list
        promotionData.products
          .filter((product: any) => product.productName && product.productName.trim() !== '')
          .forEach((product: any, index: number) => {
            const tempProduct = {
              id: product.id || `temp_${Date.now()}_${index}`,
              itemName: product.productName
            };
            this.productDataList.push(tempProduct);
          });
      }
    }


    if (promotionData.products && promotionData.products.length > 0) {
      // Filter out products with empty or null productName (deleted products)
      const validProducts = promotionData.products.filter((product: any) =>
        product.productName && product.productName.trim() !== ''
      );

      if (validProducts.length > 0) {
        this.productRows = validProducts.map((product: any, index: number) => {
        let productMatch = this.productDataList.find((p: any) => {
          const match = p.itemName === product.productName ||
            p.id === product.id ||
            p.itemName?.toLowerCase() === product.productName?.toLowerCase();

          if (match) {
          }
          return match;
        });
        if (!productMatch && product.productName) {
          productMatch = this.productDataList.find((p: any) =>
            p.itemName?.toLowerCase().includes(product.productName?.toLowerCase()) ||
            product.productName?.toLowerCase().includes(p.itemName?.toLowerCase())
          );

          if (productMatch) {
          }
        }
        if (!productMatch && product.productName) {
          productMatch = {
            id: product.id || `temp_${Date.now()}_${index}`,
            itemName: product.productName
          };
          this.productDataList.push(productMatch);
          this.cdr.detectChanges();
        }
        if (productMatch) {
          if (!productMatch.id || !productMatch.itemName) {
            productMatch = {
              id: productMatch.id || `temp_${Date.now()}_${index}`,
              itemName: productMatch.itemName || product.productName || 'Unknown Product'
            };
          }
        }

        const productRow = {
          selectedProduct: productMatch ? [productMatch] : [],
          quantity: product.quantity || null,
          productId: product.id || productMatch?.id || null,
          // Store original product data for edit mode
          originalProductData: {
            materialCode: product.materialCode || '',
            portfolio: product.portfolio || '',
            category: product.category || ''
          }
        };

        return productRow;
        });
      } else {
        // If no valid products, initialize with empty row
        this.productRows = [{
          selectedProduct: [],
          quantity: null,
          productId: null,
          originalProductData: null
        }];
      }
    } else {
      // Initialize with at least one empty row
      this.productRows = [{
        selectedProduct: [],
        quantity: null,
        productId: null,
        originalProductData: null
      }];
    }

    this.productDataList = [...this.productDataList];
    this.cdr.detectChanges();

    // Additional refresh with timing
    setTimeout(() => {
      this.productDataList = [...this.productDataList]; // Recreate reference again
      this.cdr.detectChanges();
    }, 100);
  }

  // Method to force refresh product dropdowns specifically
  forceRefreshProductDropdowns() {

    if (this.productDataList.length === 0) {
      this.loadAllProducts();
      return;
    }

    // Check each product row
    this.productRows.forEach((row, index) => {
      if (row.selectedProduct && row.selectedProduct.length > 0) {
        const product = row.selectedProduct[0];

        // Verify the product exists in productDataList
        const exists = this.productDataList.find(p => p.id === product.id);
        if (!exists) {
          this.productDataList.push(product);
        }
      }
    });

    // Final change detection
    this.cdr.detectChanges();
  }

  populateRegionAndZone(promotionData: any) {
    // Handle both single region (backward compatibility) and multiple regions
    let regionsToProcess: { id: number; itemName?: string; code?: string; name?: string }[] = [];

    if (promotionData.region) {
      if (Array.isArray(promotionData.region)) {
        // Multiple regions from new API format
        regionsToProcess = promotionData.region;
      } else {
        // Single region from old API format
        regionsToProcess = [promotionData.region];
      }
    }

    if (regionsToProcess.length > 0 && this.regionDataList.length > 0) {
      const regionMatches: any[] = [];

      regionsToProcess.forEach((regionData: { id: number; itemName?: string; code?: string; name?: string }) => {
        const regionMatch = this.regionDataList.find((r: any) => {
          return r.id == regionData.id;
        });
        if (regionMatch) {
          regionMatches.push(regionMatch);
        }
      });

      if (regionMatches.length > 0) {
        this.selectedRegion = [];
        this.selectedZone = [];
        this.zoneDataList = [];

        setTimeout(() => {
          this.selectedRegion = regionMatches;
          this.cdr.detectChanges();

          // Load zones for all selected regions and then populate zones
          this.loadZonesForSelectedRegions();

          // After zones are loaded, populate selected zones
          setTimeout(() => {
            this.populateSelectedZones(promotionData.zone);
          }, 500);
        }, 100);
      }
    }
  }

  populateSelectedZones(zoneData: any) {
    if (!zoneData) return;

    // Handle both single zone (backward compatibility) and multiple zones
    let zonesToProcess: any[] = [];

    if (Array.isArray(zoneData)) {
      // Multiple zones from new API format
      zonesToProcess = zoneData;
    } else {
      // Single zone from old API format
      zonesToProcess = [zoneData];
    }

    const zoneMatches: any[] = [];

    zonesToProcess.forEach((zone: any) => {
      const zoneMatch = this.zoneDataList.find((z: any) => {
        return z.id == zone.id;
      });
      if (zoneMatch) {
        zoneMatches.push(zoneMatch);
      } else {
        // If zone not found in current zoneDataList, create a temporary entry
        // This can happen if the zone belongs to a different region
        zoneMatches.push({
          id: zone.id,
          itemName: zone.name || zone.itemName || 'Unknown Zone',
          code: zone.code || 'TEMP' + zone.id,
          name: zone.name || zone.itemName || 'Unknown Zone'
        });
      }
    });

    if (zoneMatches.length > 0) {
      this.selectedZone = zoneMatches;
      this.cdr.detectChanges();
    }
  }

  loadZonesAndPopulate(regionId: any, zoneData: any) {
    const data = { zoneId: regionId };
    this.zoneDataList = [];

    this.userService.getZoneById(data).subscribe({
      next: (response: any) => {
        try {
          response = this.utility.decryptString(response);
          let zoneArray = JSON.parse(response);
          this.zoneDataList = zoneArray.map((item: any) => ({
            id: item.id,
            itemName: item.name,
            code: item.code,
            name: item.name
          }));
          this.cdr.detectChanges();
          if (zoneData && this.zoneDataList.length > 0) {
            setTimeout(() => {

              const zoneMatch = this.zoneDataList.find((z: any) => {
                return z.id == zoneData.id ||
                  z.id === zoneData.id ||
                  String(z.id) === String(zoneData.id) ||
                  z.name === zoneData.name ||
                  z.itemName === zoneData.name;
              });


              if (zoneMatch) {
                // Clear first to ensure clean state
                this.selectedZone = [];
                setTimeout(() => {
                  this.selectedZone = [zoneMatch];

                  // Force change detection after zone selection
                  this.cdr.detectChanges();
                }, 50);
              } else {
              }
            }, 300); // Increased delay to ensure dropdown is ready
          } else {
          }
        } catch (error) {
        }
      },
      error: (error: any) => {

        // Retry mechanism - sometimes the first call fails
        setTimeout(() => {
          this.loadZonesAndPopulate(regionId, zoneData);
        }, 1000);
      }
    });
  }

  // Helper method to retry zone selection if it fails initially
  retryZoneSelection(zoneData: any, maxRetries: number = 3) {
    if (maxRetries <= 0) {
      return;
    }


    if (this.zoneDataList.length === 0) {
      setTimeout(() => {
        this.retryZoneSelection(zoneData, maxRetries - 1);
      }, 500);
      return;
    }

    const zoneMatch = this.zoneDataList.find((z: any) =>
      z.id == zoneData.id || String(z.id) === String(zoneData.id)
    );

    if (zoneMatch) {
      this.selectedZone = [zoneMatch];
      this.cdr.detectChanges();
    } else {
      setTimeout(() => {
        this.retryZoneSelection(zoneData, maxRetries - 1);
      }, 500);
    }
  }

  // Method to force refresh all multiselect components
  refreshMultiselectComponents() {

    // Log current state

    // FAST FIX: Force refresh by recreating array references
    this.productDataList = [...this.productDataList];
    this.regionDataList = [...this.regionDataList];
    this.zoneDataList = [...this.zoneDataList];

    // Detailed product data logging
    if (this.productDataList.length > 0) {
    } else {
      this.loadAllProducts();
    }

    // Product rows validation check
    if (this.productRows && this.productRows.length > 0) {
      // Product rows are available for validation
    }

    // Force change detection multiple times with delays
    this.cdr.detectChanges();

    setTimeout(() => {
      this.cdr.detectChanges();

      setTimeout(() => {
        this.cdr.detectChanges();

        setTimeout(() => {
          this.cdr.detectChanges();
        }, 200);
      }, 200);
    }, 200);
  }

  onFileSelect(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        this.toastr.error('Please select a valid image file (JPEG, PNG, GIF)');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        this.toastr.error('Image size should be less than 10 MB');
        return;
      }

      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.uploadImage();
    }
  }

  uploadImage() {
    if (!this.selectedFile) return;

    this.isImageUploading = true;
    this.spinner.show();

    const formData = new FormData();
    // Changed from 'file' to 'image' to match backend expectation
    formData.append('image', this.selectedFile);

    this.promotionsService.uploadPromotionImages(formData).subscribe({
      next: (response: any) => {
        response = this.utility.decryptString(response);
        try {
          const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response;

          // Store both image URL and ID
          this.uploadedImageUrl = parsedResponse.imageUrl || parsedResponse.url || parsedResponse.message || parsedResponse;
          this.uploadedImageId = parsedResponse.id || parsedResponse.imageId || null;


          this.toastr.success('Image uploaded successfully!');
        } catch (error) {
          this.uploadedImageUrl = response;
          if (typeof response === 'number') {

            this.uploadedImageId = response;
            response = this.utility.decryptString(response)
          } else if (typeof response === 'string' && !isNaN(Number(response))) {
            this.uploadedImageId = Number(response);
          }

          this.toastr.success('Image uploaded successfully!');
        }
        this.isImageUploading = false;
        this.spinner.hide();
      },
      error: (error: any) => {
        let errorMessage = 'Failed to upload image. Please try again.';

        // Provide more specific error messages if available
        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.status === 400) {
          errorMessage = 'Invalid request. Please check the file and try again.';
        } else if (error.status === 413) {
          errorMessage = 'File too large. Please select a smaller image.';
        }

        this.toastr.error(errorMessage);
        this.isImageUploading = false;
        this.selectedFile = null;
        this.selectedFileName = '';
        this.spinner.hide();
      }
    });
  }



  canAddMore(): boolean {
    if (this.productRows.length === 0) {
      return false;
    }
    const allRowsFilled = this.productRows.every(row => {
      const hasProduct = row.selectedProduct && Array.isArray(row.selectedProduct) && row.selectedProduct.length > 0;
      const hasQuantity = row.quantity && (typeof row.quantity === 'number' ? row.quantity > 0 : parseInt(row.quantity) > 0);
      return hasProduct && hasQuantity;
    });

    return allRowsFilled;
  }
  isFormValid(): boolean {
    const promotionNameValid = this.promotionForm.get('promotionName')?.value?.trim()?.length > 0;
    const startDateValid = !!this.promotionForm.get('startDate')?.value;
    const endDateValid = !!this.promotionForm.get('endDate')?.value;
    const distributorValid = this.selectedDistributor && Array.isArray(this.selectedDistributor) && this.selectedDistributor.length > 0;
    const pointsValid = this.promotionForm.get('points')?.value > 0;
    const regionValid = this.selectedRegion && Array.isArray(this.selectedRegion) && this.selectedRegion.length > 0;
    const zoneValid = this.selectedZone && Array.isArray(this.selectedZone) && this.selectedZone.length > 0;
    const imageValid = !!(this.uploadedImageId || this.selectedFileName || this.uploadedImageUrl);
    let productValid = false;
    if (this.productRows && this.productRows.length > 0) {
      productValid = this.productRows.some(row => {
        const hasProduct = row.selectedProduct && Array.isArray(row.selectedProduct) && row.selectedProduct.length > 0;
        const hasQuantity = row.quantity && (typeof row.quantity === 'number' ? row.quantity > 0 : parseInt(row.quantity) > 0);
        return hasProduct && hasQuantity;
      });
    }
    const formControlsValid = promotionNameValid && startDateValid && endDateValid && distributorValid && pointsValid;
    const allValid = formControlsValid && regionValid && zoneValid && imageValid && productValid;


    return allValid;
  }
  triggerFormValidation(): void {
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 50);
  }

  isFormValidSimple(): boolean {
    const hasPromotionName = !!this.promotionForm?.get('promotionName')?.value;
    const hasDistributor = this.selectedDistributor?.length > 0;
    const hasPoints = !!this.promotionForm?.get('points')?.value;
    const hasStartDate = !!this.promotionForm?.get('startDate')?.value;
    const hasEndDate = !!this.promotionForm?.get('endDate')?.value;
    const hasRegion = this.selectedRegion?.length > 0;
    const hasZone = this.selectedZone?.length > 0;
    const hasImage = !!(this.uploadedImageId || this.selectedFileName || this.uploadedImageUrl);
    const hasValidProducts = this.areAllProductRowsValid();

    return hasPromotionName && hasDistributor && hasPoints &&
      hasStartDate && hasEndDate && hasRegion && hasZone &&
      hasImage && hasValidProducts;
  }

  areAllProductRowsValid(): boolean {
    if (!this.productRows || this.productRows.length === 0) {
      return false;
    }

    const allRowsValid = this.productRows.every((row) => {
      const hasProduct = row.selectedProduct && Array.isArray(row.selectedProduct) && row.selectedProduct.length > 0;
      const hasQuantity = row.quantity && (typeof row.quantity === 'number' ? row.quantity > 0 : parseInt(row.quantity) > 0);
      return hasProduct && hasQuantity;
    });

    return allRowsValid;
  }

  isFormDataValid(): boolean {
    // Check if required selections are made
    if (!this.selectedRegion.length || !this.selectedZone.length) {
      return false;
    }

    // Check if at least one product row has both product and quantity
    const hasValidProduct = this.productRows.some(row =>
      row.selectedProduct.length > 0 && row.quantity && row.quantity > 0
    );

    return hasValidProduct;
  }

  isSubmitEnabled(): boolean {
    if (!this.promotionForm.valid) {
      return false;
    }
    if (!this.selectedRegion.length || !this.selectedZone.length) {
      return false;
    }
    const hasValidProduct = this.productRows.some(row =>
      row.selectedProduct.length > 0 && row.quantity && row.quantity > 0
    );
    if (!hasValidProduct) {
      return false;
    }
    if (this.isImageUploading) {
      return false;
    }
    if (!this.isEditMode && !this.uploadedImageId) {
      return false;
    }

    return true;
  }


  addMoreProduct(): void {
    if (this.canAddMore()) {
      this.additionalProducts.push({
        selectedProduct: [],
        quantity: null,
        productId: null
      });
    }
  }

  addProductRow(): void {
    this.addMoreProductRow();
  }



  onSubmitPromotion(): void {
    if (this.isFormValid()) {
      let promotionPayload: any = {
        promotionName: this.promotionForm.get('promotionName')?.value,
        startDate: this.promotionForm.get('startDate')?.value
          ? moment(this.promotionForm.get('startDate')?.value).format('YYYY-MM-DD')
          : null,
        endDate: this.promotionForm.get('endDate')?.value
          ? moment(this.promotionForm.get('endDate')?.value).format('YYYY-MM-DD')
          : null,
        region: this.selectedRegion.map(region => ({
          id: region.id
        })),
        zone: this.selectedZone.map(zone => ({
          id: zone.id
        })),
        distributorsInvolved: this.selectedDistributor.map(distributor => ({
          id: distributor.id
        })),
        points: this.promotionForm.get('points')?.value,
        isActive: true,
        products: [
          // Include current product rows
          ...this.productRows
            .filter(row => row.selectedProduct.length && row.quantity)
            .map(row => {
              const selectedProduct = row.selectedProduct[0];
              const productData = this.DataList.find(p => p.id === selectedProduct.id);

              let productPayload: any = {
                productName: selectedProduct.itemName,
                quantity: row.quantity
              };

              if (this.isEditMode && row.originalProductData) {
                productPayload.materialCode = row.originalProductData.materialCode || '';
                productPayload.portfolio = row.originalProductData.portfolio || '';
                productPayload.category = row.originalProductData.category || '';
                productPayload.id = row.productId;
              } else {
                productPayload.materialCode = productData?.materialCode || '';
                productPayload.portfolio = productData?.portfolio || '';
                productPayload.category = productData?.category || '';
                if (this.isEditMode && row.productId) {
                  productPayload.id = row.productId;
                }
              }

              return productPayload;
            }),
          // Include deleted products with empty fields (only in edit mode)
          ...(this.isEditMode && this.deletedProductIds ? this.deletedProductIds.map(deletedProduct => ({
            id: deletedProduct.id,
            productName: '', // Empty product name for deletion
            quantity: null, // Empty quantity for deletion
            materialCode: deletedProduct.originalProductData?.materialCode || '',
            portfolio: deletedProduct.originalProductData?.portfolio || '',
            category: deletedProduct.originalProductData?.category || ''
          })) : [])
        ],
        promotionImages: [
          {
            imageId: this.uploadedImageId
          }
        ],
        isAssigned: true
      };
      if (this.isEditMode && this.editingPromotionId) {
        promotionPayload.id = this.editingPromotionId;
      }
      if (this.isProcessingSubmit) {
        return;
      }

      this.isProcessingSubmit = true;
      const wasEditMode = this.isEditMode;
      this.spinner.show();
      const apiCall = this.isEditMode
        ? this.promotionsService.updatePromotion(promotionPayload)
        : this.promotionsService.addPromotion(promotionPayload);

      apiCall.subscribe({
        next: (response: any) => {

          this.spinner.hide();
          this.isProcessingSubmit = false;
          this.closePromotionPopup();
          this.toastr.success(wasEditMode ? 'Promotion updated successfully!' : 'Promotion added successfully!');
          setTimeout(() => {
            this.getPromotionData();
          }, 100);
        },
        error: (error: any) => {
          console.error('Failed to submit promotion', error);
          this.spinner.hide();
          this.isProcessingSubmit = false;
          let errorMessage = wasEditMode
            ? 'Failed to update promotion. Please try again.'
            : 'Failed to create promotion. Please try again.';

          try {
            if (error.error) {
              const decryptedError = this.utility.decryptString(error.error);
              const errorObj = JSON.parse(decryptedError);
              errorMessage = errorObj.message || errorMessage;
            }
          } catch (decryptError) {
            console.error('Failed to decrypt error response:', decryptError);
            // Fallback to original error handling
            if (error.error?.message) {
              errorMessage = error.error.message;
            }
          }

          this.toastr.error(errorMessage);
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.promotionForm.controls).forEach(key => {
        this.promotionForm.get(key)?.markAsTouched();
      });
    }
  }

  closePromotionPopup() {
    // Clear all form data when popup is closed
    this.clearFormData();
    if (this.addEditPromotionDialogRef) {
      this.addEditPromotionDialogRef.close();
    }
  }

  clearFormData(): void {
    // Reset form
    this.promotionForm.reset();

    // Clear multiselect data
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedProduct = [];
    this.selectedDistributor = [];

    // Clear additional products and product rows
    this.additionalProducts = [];
    this.productRows = [];

    // Clear zone data list (this is specific to selected region)
    this.zoneDataList = [];

    // DON'T clear product data lists - these are shared between add/edit
    // this.productDataList = [];
    // this.DataList = [];


    // Clear file selection and image upload data
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;

    // Reset edit mode and processing flag
    this.isEditMode = false;
    this.isProcessingSubmit = false;
  }


  getPromotionData(page?: any) {
    const VIEW_ICON_PATH = 'assets/img/product.svg';
    this.supportService.setButtonState('new');
    this.spinner.show();
    const currentPage = page !== undefined ? page : this.currentPage || 0;
    const pageSize = this.perPage || 20;

    let data = {
      searchedValue: this.searchedValue || '',
      unpaged: false,
      currentPage: currentPage,
      pageLimit: pageSize,
    };


    this.promotionsService.getPromotions(data).subscribe({
      next: (response: any) => {
        try {
          response = this.utility.decryptString(response);
          let parsedResponse = response;

          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }
          if (parsedResponse && parsedResponse.encryptedBody) {
            try {
              const decrypted = this.utility.decryptString(parsedResponse.encryptedBody);
              parsedResponse = JSON.parse(decrypted);

            } catch (decryptError) {
              this.handleDecryptionError('Failed to decrypt response data', decryptError);
              return;
            }
          } 

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;
            parsedResponse.content.forEach((promotionInfo: any) => {
              this.showIndex = i++;

              // Format products display
              let productsDisplay = 'No Products';
              if (promotionInfo.products && promotionInfo.products.length > 0) {
                productsDisplay = promotionInfo.products.map((p: any) => p.productName).join(', ');
                if (productsDisplay.length > 50) {
                  productsDisplay = productsDisplay.substring(0, 50) + '...';
                }
              }

              // Format region display - handle array of regions
              let regionDisplay = 'NA';
              if (promotionInfo.region && Array.isArray(promotionInfo.region) && promotionInfo.region.length > 0) {
                regionDisplay = promotionInfo.region.map((r: any) => r.name).join(', ');
                if (regionDisplay.length > 50) {
                  regionDisplay = regionDisplay.substring(0, 50) + '...';
                }
              }

              // Format zone display - handle array of zones
              let zoneDisplay = 'NA';
              if (promotionInfo.zone && Array.isArray(promotionInfo.zone) && promotionInfo.zone.length > 0) {
                zoneDisplay = promotionInfo.zone.map((z: any) => z.name).join(', ');
                if (zoneDisplay.length > 50) {
                  zoneDisplay = zoneDisplay.substring(0, 50) + '...';
                }
              }

              // Format distributor display - handle array of distributors
              let distributorDisplay = 'NA';
              if (promotionInfo.distributorsInvolved && Array.isArray(promotionInfo.distributorsInvolved) && promotionInfo.distributorsInvolved.length > 0) {
                distributorDisplay = promotionInfo.distributorsInvolved.map((d: any) => d.distributorName).join(', ');
                if (distributorDisplay.length > 50) {
                  distributorDisplay = distributorDisplay.substring(0, 50) + '...';
                }
              } else if (promotionInfo.distributorName) {
                distributorDisplay = promotionInfo.distributorName;
              }

              let promotionObj = {
                id: promotionInfo.id || 'NA',
                promotionName: this.utility.toUpperCaseUtil(promotionInfo.promotionName) || 'NA',
                products: VIEW_ICON_PATH, // Keep it simple for the dynamic table
                distributorName: this.utility.toUpperCaseUtil(distributorDisplay),
                region: regionDisplay,
                zone: zoneDisplay,
                startDate: moment(promotionInfo.startDate).format('DD-MM-YYYY') || promotionInfo.startDate || 'NA',
                endDate: moment(promotionInfo.endDate).format('DD-MM-YYYY') || promotionInfo.endDate || 'NA',
                points: promotionInfo.points || 0,
                status: this.utility.capitalizeWords(promotionInfo.status) || 'NA',
                isAssigned: promotionInfo.isAssigned, // Add isAssigned for button control
                isDisabled: promotionInfo.isAssigned === false || promotionInfo.isAssigned === "false" || promotionInfo.isAssigned === 0 || promotionInfo.isAssigned === "0", // Add isDisabled for button control

                // Store full data for popups and actions
                fullData: promotionInfo
              };
              this.tableData.push(promotionObj);
            });
            this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
            this.promotionsCount = parsedResponse.totalElements;
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.promotionsCount = 0;
            this.tableData = [];
            this.newFlag = false;
          }
          localStorage.setItem('newFlag', JSON.stringify(this.newFlag));
          this.events.setChangedContentTopText('Promotions Management');
          this.newFlag = true;

          this.spinner.hide();
        } catch (error) {
          this.handleDecryptionError('Failed to process promotions data', error);
        }
      },
      error: (errorResponse: any) => {
        this.handleApiError(errorResponse);
        this.spinner.hide();
      },
    });
  }

  /**
   * Helper method to fetch distributor data for a progress item
   */
  private async fetchDistributorData(promotionProductId: number, promotionId: number): Promise<{name: string, code: string}[]> {
    try {
      const response = await this.promotionsService.getDistributorsList(promotionProductId, promotionId).toPromise();
      if (response) {
        const decryptedResponse = this.utility.decryptString(response);
        const parsedResponse = JSON.parse(decryptedResponse);
        return parsedResponse || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching distributor data:', error);
      return [];
    }
  }

  /**
   * API call for progress tab
   * @param page
   */
  getProgress(page?: any) {
    this.supportService.setButtonState('progress');
    this.selectedValue = '2';
    this.spinner.show();
    this.newFlag = false;

    // Ensure proper default values for progress API
    const currentPage = page !== undefined ? page : this.currentPage || 0;
    const pageSize = this.perPage || 1;

    let data = {
      unpaged: true,
      currentPage: currentPage,
      pageLimit: pageSize,
      searchedValue: this.searchedValue || '',
    };


    this.promotionsService.getPromotionProgress(data).subscribe({
      next: (res: any) => {
        try {
         res = this.utility.decryptString(res);
          let parsedResponse = typeof res === 'string' ? JSON.parse(res) : res;
          if (typeof res === 'string') {
            try {
              const decrypted = res;
              parsedResponse = JSON.parse(decrypted);
            } catch (decryptError) {
            }
          }


          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            this.tableData = [];
            let i = 1;

            // Process each progress item and fetch distributor data
            const progressPromises = parsedResponse.content.map(async (progressInfo: any) => {
              this.showIndex = i++;

              // Fetch distributor data for this progress item
              let distributorData: {name: string, code: string}[] = [];
              if (progressInfo.promotionProductId && progressInfo.promotionId) {
                distributorData = await this.fetchDistributorData(progressInfo.promotionProductId, progressInfo.promotionId);
              }

              // Get first distributor or default values
              const firstDistributor = distributorData.length > 0 ? distributorData[0] : { name: 'NA', code: 'NA' };

              let progressObj = {
                id: progressInfo.id || 'NA',
                promotionName: progressInfo.promotionName || 'NA',
                products: 'assets/img/product.svg', // Show product icon
                distributorName: firstDistributor.name,
                region: progressInfo.region || 'NA',
                zone: progressInfo.zone || 'NA',
                startDate: 'NA', // Not available in progress response
                endDate: 'NA', // Not available in progress response
                points: 'NA', // Not available in progress response
                status: 'In Progress',

                // Progress specific fields
                leaderName: this.utility.toUpperCaseUtil(progressInfo.leaderName) || 'NA',
                productName: this.utility.toUpperCaseUtil(progressInfo.productName) || progressInfo.productName || 'NA',
                portfolio: progressInfo.portfolio || 'NA',
                category: this.utility.toUpperCaseUtil(progressInfo.category) || 'NA',
                totalQuantity: progressInfo.totalQuantity || 0,
                achievedQuantity: progressInfo.achievedQuantity || 0,
                bonificationAmount:  (progressInfo.bonificationAmount) || progressInfo.bonificationAmount || 0,
                distributorCode: firstDistributor.code,

                // Store full data for popups and actions
                fullData: progressInfo
              };

              return progressObj;
            });

            // Wait for all distributor data to be fetched
            Promise.all(progressPromises).then((processedData) => {
              this.tableData = processedData;
              this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
              this.progressCount = parsedResponse.totalElements;
              this.spinner.hide();
            }).catch((error) => {
              console.error('Error processing distributor data:', error);
              // Fallback: use original data without distributor info
              this.tableData = parsedResponse.content.map((progressInfo: any, index: number) => ({
                id: progressInfo.id || 'NA',
                promotionName: progressInfo.promotionName || 'NA',
                products: 'assets/img/product.svg',
                distributorName: 'NA',
                region: progressInfo.region || 'NA',
                zone: progressInfo.zone || 'NA',
                startDate: 'NA',
                endDate: 'NA',
                points: 'NA',
                status: 'In Progress',
                leaderName: this.utility.toUpperCaseUtil(progressInfo.leaderName) || 'NA',
                productName: this.utility.toUpperCaseUtil(progressInfo.productName) || progressInfo.productName || 'NA',
                portfolio: progressInfo.portfolio || 'NA',
                category: progressInfo.category || 'NA',
                totalQuantity: progressInfo.totalQuantity || 0,
                achievedQuantity: progressInfo.achievedQuantity || 0,
                bonificationAmount: (progressInfo.bonificationAmount) || progressInfo.bonificationAmount || 0,
                distributorCode: 'NA',
                fullData: progressInfo
              }));
              this.configurationSettings.totalRecordCount = parsedResponse.totalElements;
              this.progressCount = parsedResponse.totalElements;
              this.spinner.hide();
            });
          } else {
            this.configurationSettings.totalRecordCount = 0;
            this.progressCount = 0;
            this.tableData = [];
            this.progressFlag = false;
          }
          this.events.setChangedContentTopText('Promotions Management');
          this.progressFlag = false;
          this.spinner.hide();
        } catch (error) {
          this.configurationSettings.totalRecordCount = 0;
          this.progressCount = 0;
          this.tableData = [];
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        this.handleApiError(errorResponse);
      },
    });
  }

  /**
   * API call for exporting data in CSV format
   */
  getPromotionExportData() {
    window.scrollTo(0, 0);
    this.spinner.show();

    // Determine which API to call based on current tab
    let apiCall;
    let data;

    if (this.isNewTicket) {
      // Export Promotions tab data
      data = {
        searchedValue: this.searchedValue || '',
        unpaged: true,
        currentPage: 0,
        pageLimit: 999999, // Large number to get all records
      };
      apiCall = this.promotionsService.getPromotions(data);
    } else {
      // Export Progress tab data
      data = {
        unpaged: true,
        currentPage: 0,
        pageLimit: 999999, // Large number to get all records
        sort: 'string'
      };
      apiCall = this.promotionsService.getPromotionProgress(data);
    }

    apiCall.subscribe({
      next: (exportData: any) => {
        try {
          // Decrypt and parse response
          let decryptedResponse = this.utility.decryptString(exportData);
          let parsedResponse = JSON.parse(decryptedResponse);

          this.exportData = [];

          if (parsedResponse && parsedResponse.content && parsedResponse.content.length) {
            if (this.isNewTicket) {
              // Export Promotions data
              parsedResponse.content.forEach((promotionInfo: any) => {
                let exportObj = {
                  promotionName: this.utility.toUpperCaseUtil(promotionInfo.promotionName) || promotionInfo.promotionName || 'NA',
                  distributorName: this.utility.toUpperCaseUtil(promotionInfo.distributorName) || promotionInfo.distributorName || 'NA',
                  region: promotionInfo.region?.name || 'NA',
                  zone: promotionInfo.zone?.name || 'NA',
                  startDate: moment(promotionInfo.startDate).format('DD-MM-YYYY') || promotionInfo.startDate || 'NA',
                  endDate: moment(promotionInfo.endDate).format('DD-MM-YYYY') || promotionInfo.endDate || 'NA',
                  points: promotionInfo.points || 0,
                  status: promotionInfo.isActive ? 'Active' : 'Inactive',
                  products: promotionInfo.products?.map((p: any) => p.productName).join(', ') || 'NA'
                };
                this.exportData.push(exportObj);
              });

              let options = {
                fieldSeparator: ',',
                quoteStrings: '"',
                decimalseparator: '.',
                showLabels: true,
                headers: [
                  'Promotion Name',
                  'Distributor Name',
                  'Region',
                  'Zone',
                  'Start Date',
                  'End Date',
                  'Points Multiplier',
                  'Status',
                  'Products'
                ],
              };
              new ngxCsv(this.exportData, 'Promotions', options);
            } else {
              // Export Progress data with distributor information
              const exportPromises = parsedResponse.content.map(async (progressInfo: any) => {
                // Fetch distributor data for this progress item
                let distributorData: {name: string, code: string}[] = [];
                if (progressInfo.promotionProductId && progressInfo.promotionId) {
                  distributorData = await this.fetchDistributorData(progressInfo.promotionProductId, progressInfo.promotionId);
                }

                // Get first distributor or default values
                const firstDistributor = distributorData.length > 0 ? distributorData[0] : { name: 'NA', code: 'NA' };

                let exportObj = {
                  promotionName: this.utility.toUpperCaseUtil(progressInfo.promotionName) || progressInfo.promotionName || 'NA',
                  leaderName: this.utility.toUpperCaseUtil(progressInfo.leaderName) || progressInfo.leaderName || 'NA',
                  productName: this.utility.toUpperCaseUtil(progressInfo.productName) || progressInfo.productName || 'NA',
                  portfolio: progressInfo.portfolio || 'NA',
                  category: progressInfo.category || 'NA',
                  totalQuantity: progressInfo.totalQuantity || 0,
                  achievedQuantity: progressInfo.achievedQuantity || 0,
                  bonificationAmount: this.numberFormatService.formatNumber(progressInfo.bonificationAmount) || progressInfo.bonificationAmount || 0,
                  distributorName: firstDistributor.name,
                  distributorCode: firstDistributor.code
                };
                return exportObj;
              });

              // Wait for all distributor data to be fetched for export
              Promise.all(exportPromises).then((exportData) => {
                this.exportData = exportData;

                let options = {
                  fieldSeparator: ',',
                  quoteStrings: '"',
                  decimalseparator: '.',
                  showLabels: true,
                  headers: [
                    'Promotion Name',
                    'Leader Name',
                    'Product Name',
                    'Portfolio',
                    'Category',
                    'Total Quantity',
                    'Achieved Quantity',
                    'Bonification Amount (Mex$)',
                    'Distributor Name',
                    'Distributor Code'
                  ],
                };
                new ngxCsv(this.exportData, 'Promotion Progress', options);
                this.spinner.hide();
              }).catch((error) => {
                console.error('Error fetching distributor data for export:', error);
                // Fallback: export without distributor data
                this.exportData = parsedResponse.content.map((progressInfo: any) => ({
                  promotionName: this.utility.toUpperCaseUtil(progressInfo.promotionName) || progressInfo.promotionName || 'NA',
                  leaderName: this.utility.toUpperCaseUtil(progressInfo.leaderName) || progressInfo.leaderName || 'NA',
                  productName: this.utility.toUpperCaseUtil(progressInfo.productName) || progressInfo.productName || 'NA',
                  portfolio: progressInfo.portfolio || 'NA',
                  category:  this.utility.toUpperCaseUtil(progressInfo.category) || 'NA',
                  totalQuantity: progressInfo.totalQuantity || 0,
                  achievedQuantity: progressInfo.achievedQuantity || 0,
                  bonificationAmount: this.numberFormatService.formatNumber(progressInfo.bonificationAmount) || progressInfo.bonificationAmount || 0,
                  distributorName: 'NA',
                  distributorCode: 'NA'
                }));

                let options = {
                  fieldSeparator: ',',
                  quoteStrings: '"',
                  decimalseparator: '.',
                  showLabels: true,
                  headers: [
                    'Promotion Name',
                    'Leader Name',
                    'Product Name',
                    'Portfolio',
                    'Category',
                    'Total Quantity',
                    'Achieved Quantity',
                    'Bonification Amount (Mex$)',
                    'Distributor Name',
                    'Distributor Code'
                  ],
                };
                new ngxCsv(this.exportData, 'Promotion Progress', options);
                this.spinner.hide();
              });
            }
          } else {
            this.toastr.warning('No data available for export');
            this.spinner.hide();
          }
        } catch (error) {
          this.toastr.warning('No data available for export');
          this.spinner.hide();
        }
      },
      error: (errorResponse: any) => {
        this.handleApiError(errorResponse);
      },
    });
  }



  functionForName(event: any) {
    const k = event.charCode || event.keyCode;
    const lastChar = event.target.value.slice(-1);

    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }

    if (lastChar === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    // Allow letters (uppercase and lowercase), numbers, spaces, and specified characters
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k === 32 || // space
      (k >= 48 && k <= 57) || // numbers
      k === 8 || // backspace
      k === 95 || // underscore
      k === 45 || // hyphen
      k === 58 || // colon
      k === 46 || // period
      k === 44 || // comma
      k === 63 || // question mark
      k === 34 || // double quote
      k === 39 || // single quote
      k === 40 || // open parenthesis
      k === 41 || // close parenthesis
      k === 91 || // open square bracket
      k === 93 || // close square bracket
      k === 38 || // ampersand
      k === 42 // asterisk
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }


  /**
   * Triggered when submit button clicked
   * @param formData
   */
  onSubmit() {
    if (this.formGroup.valid) {
      const formData = this.formGroup.value;
      const dataToSend = {
        ticketId: (JSON.stringify(this.ticketId)),
        status: (this.selectedStatus),
        category: (this.selectedDialogCategory),
        comment: (formData.comments),
      };
      this.spinner.show();
      this.supportService.getUpdateTicket(dataToSend).subscribe({
        next: (ticketResponse: any) => {
          try {
            // First, check if response is a string and parse it if needed
            let parsedResponse = ticketResponse;
            if (typeof ticketResponse === 'string') {
              parsedResponse = JSON.parse(ticketResponse);
            }

            // Check if response has encryptedBody property
            if (parsedResponse && parsedResponse.encryptedBody) {
              // Decrypt the encryptedBody
              const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
              parsedResponse = JSON.parse(decrypted);
            } else {
              // Try to decrypt the entire response if needed
              if (typeof ticketResponse === 'string') {
                const decrypted = this.utility.decrypt(ticketResponse);
                parsedResponse = JSON.parse(decrypted);
              }
            }

            this.toastr.success(parsedResponse.message);
            this.onCloseForm();
            if (this.isNewTicket) {
              this.isNewTicket = true;
              this.isprogressTicket = false;
              this.getPromotionData();
            } else {
              this.isNewTicket = false;
              this.isprogressTicket = true;
              this.getProgress();
            }
            this.setTableHeader();
          } catch (error) {
            this.toastr.error('An error occurred while updating the ticket');
          }
          this.spinner.hide();
        },
        error: (errorResponse: any) => {
          this.handleApiError(errorResponse);
        },
      });
    } else {
      // Mark all form controls as touched to trigger validation messages
      Object.keys(this.formGroup.controls).forEach(key => {
        const control = this.formGroup.get(key);
        control?.markAsTouched();
      });
    }
  }

  showTooltipIfTruncated(event: MouseEvent): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.scrollWidth > inputElement.clientWidth) {
      inputElement.title = inputElement.value;
    } else {
      inputElement.removeAttribute("title");
    }
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  cancelApprove() {
    this.approvePromotionDialogRef.close();
  }

  cancelReject() {
    this.rejectComment = ''; // Clear the comment when canceling
    this.rejectPromotionDialogRef.close();
  }

  submitApprove() {
    if (!this.selectedPromotionData) {
      this.toastr.warning('Please select a promotion to approve');
      return;
    }

    this.spinner.show();

    this.promotionsService.approvePromotion(this.selectedPromotionData.id).subscribe({
      next: (response: any) => {
        this.spinner.hide();
        this.toastr.success('Promotion approved successfully');
        this.approvePromotionDialogRef.close();
        this.getPromotionData(); // Refresh the promotions list
      },
      error: (errorResponse: any) => {
        this.spinner.hide();

        // Check if error was already handled by auth interceptor
        if (this.isInterceptorHandledError(errorResponse)) {
          return; // Don't show duplicate error message
        }

        let errorMessage = 'Failed to approve promotion';

        try {
          if (errorResponse.error) {
            const decryptedError = this.utility.decryptString(errorResponse.error);
            const errorObj = JSON.parse(decryptedError);
            errorMessage = errorObj.message || errorMessage;
          }
        } catch (e) {
        }

        this.toastr.error(errorMessage);
      }
    });
  }

  submitReject() {
    if (!this.selectedPromotionData) {
      this.toastr.warning('Please select a promotion to reject');
      return;
    }

    if (!this.rejectComment.trim()) {
      this.toastr.warning('Please enter a comment');
      return;
    }

    this.spinner.show();

    this.promotionsService.rejectPromotion(this.selectedPromotionData.id, this.rejectComment).subscribe({
      next: (response: any) => {
        this.spinner.hide();
        this.toastr.success('Promotion rejected successfully');
        this.rejectPromotionDialogRef.close();
        this.rejectComment = ''; // Clear the comment
        this.getPromotionData(); // Refresh the promotions list
      },
      error: (errorResponse: any) => {
        this.spinner.hide();

        // Check if error was already handled by auth interceptor
        if (this.isInterceptorHandledError(errorResponse)) {
          return; // Don't show duplicate error message
        }

        let errorMessage = 'Failed to reject promotion';

        try {
          if (errorResponse.error) {
            const decryptedError = this.utility.decryptString(errorResponse.error);
            const errorObj = JSON.parse(decryptedError);
            errorMessage = errorObj.message || errorMessage;
          }
        } catch (e) {
        }

        this.toastr.error(errorMessage);
      }
    });
  }

  addMoreProductRow() {
    if (this.canAddMore()) {
      this.productRows.push({
        selectedProduct: [],
        quantity: null,
        productId: null,
        originalProductData: null
      });
      this.triggerFormValidation();
    }
  }

  // Method to delete a product row
  deleteProductRow(index: number): void {
    if (this.canDeleteRow(index)) {
      const row = this.productRows[index];

      // If in edit mode and the row has a productId, store it for deletion API call
      if (this.isEditMode && row.productId) {
        // Store deleted product ID to send with empty fields to API
        if (!this.deletedProductIds) {
          this.deletedProductIds = [];
        }
        this.deletedProductIds.push({
          id: row.productId,
          originalProductData: row.originalProductData
        });
      }

      // Create a new array without the deleted row to force Angular to re-render
      const newProductRows = [...this.productRows];
      newProductRows.splice(index, 1);
      this.productRows = newProductRows;

      // Force change detection to update the UI immediately
      this.cdr.detectChanges();

      // Trigger form validation
      this.triggerFormValidation();

      // Additional UI refresh with a small delay to ensure complete re-render
      setTimeout(() => {
        this.cdr.detectChanges();
      }, 100);
    }
  }

  // Method to check if a row can be deleted
  canDeleteRow(index: number): boolean {
    return this.productRows.length > 1 && index > 0; // Can delete if more than 1 row and not the first row
  }



  // Input validation methods using modern event.key approach
  validateTextInput(event: KeyboardEvent): boolean {
    const key = event.key;

    // Allow control keys (backspace, delete, tab, escape, enter, arrow keys, etc.)
    const controlKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];
    if (controlKeys.includes(key)) {
      return true;
    }

    // Only allow letters (a-z, A-Z), numbers (0-9), and spaces
    const allowedPattern = /^[a-zA-Z0-9\s]$/;

    // Block all special characters and symbols
    if (!allowedPattern.test(key)) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  validateNumberInput(event: KeyboardEvent): boolean {
    const key = event.key;

    // Allow control keys (backspace, delete, tab, escape, enter, arrow keys, etc.)
    const controlKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];

    if (controlKeys.includes(key)) {
      return true;
    }

    // Allow only numbers (0-9)
    const allowedPattern = /^[0-9]$/;

    if (!allowedPattern.test(key)) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  // Load all products independently (not dependent on region/zone)
  loadAllProducts() {
    this.spinner.show();
    // Call the existing API to get all products - this API gets all products regardless of the parameter
    this.rewardPointService.getProductByID({}).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio: item.portfolio || '',
          }));

          // Update productDataList for multiselect dropdown
          this.productDataList = this.DataList.map((item: any) => ({
            id: item.id,
            itemName: item.itemName
          }));
        }
        this.spinner.hide();
      },
      error: (error: any) => {
        // Fallback: keep productDataList empty if API fails
        this.productDataList = [];
        this.spinner.hide();
      }
    });
  }

  // Reset promotion dialog data
  resetPromotionDialog() {
    this.productRows = [];
    this.deletedProductIds = []; // Clear deleted products
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = []; // Clear zone data list
    this.selectedProduct = [];
    this.selectedDistributor = [];
    this.distributorDataList = []; // Clear distributor data list
    this.additionalProducts = [];
    this.selectedFileName = '';
    this.selectedFile = null;
    this.uploadedImageUrl = '';
    this.uploadedImageId = null;
    this.isImageUploading = false;
    this.editingPromotionId = null;
    this.isEditMode = false;
  }



  approvePromotion(data: any) {
    this.selectedPromotionData = data;
    this.approvePromotionDialogRef = this.dialog.open(this.approvePromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  rejectPromotion(data: any) {
    this.selectedPromotionData = data;
    this.rejectComment = ''; // Clear comment when opening dialog
    this.rejectPromotionDialogRef = this.dialog.open(this.rejectPromotionTemplate, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  /**
   * Check if error was already handled by auth interceptor to prevent duplicate error messages
   */
  private isInterceptorHandledError(errorResponse: any): boolean {
    // Auth interceptor only handles 401 and 403 for promotion APIs now
    const interceptorHandledStatuses = [401, 403];

    return interceptorHandledStatuses.includes(errorResponse.status);
  }


  // Helper method for handling decryption errors
  private handleDecryptionError(message: string, error: any): void {
    this.configurationSettings.totalRecordCount = 0;
    this.promotionsCount = 0;
    this.progressCount = 0;
    this.tableData = [];
    this.spinner.hide();
    this.toastr.error(message);
  }

  // Helper method for handling API errors
  private handleApiError(errorResponse: any): void {
    try {
      let error = errorResponse.error;

      // Check if error response has encryptedBody
      if (typeof error === 'string') {
        error = JSON.parse(error);
      }

      if (error && error.encryptedBody) {
        try {
          const decrypted = this.utility.decryptStringError(error.encryptedBody);
          error = JSON.parse(decrypted);
        } catch (decryptError) {
          // Try alternative decryption method
          try {
            const alternativeDecrypted = this.utility.decryptErrorMessage({ error: { encryptedBody: error.encryptedBody } });
            error = { message: alternativeDecrypted };
          } catch (altError) {
            error = { message: 'Failed to decrypt error message' };
          }
        }
      } else if (typeof errorResponse.error === 'string') {
        try {
          const decrypted = this.utility.decryptStringError(errorResponse.error);
          error = JSON.parse(decrypted);
        } catch (decryptError) {
          error = { message: 'Failed to decrypt error response' };
        }
      }

      if (error && error.message) {
        this.toastr.error(error.message);
      } else {
        this.toastr.error('An error occurred while processing your request');
      }

      // Handle authentication errors
      if (errorResponse.status === 401) {
        localStorage.clear();
        this.router.navigate(['']);
        this.toastr.success('Signed Out Successfully');
      }
    } catch (e) {
      this.toastr.error('An unexpected error occurred');
    }

    // Reset counts and data on error
    this.configurationSettings.totalRecordCount = 0;
    this.promotionsCount = 0;
    this.progressCount = 0;
    this.tableData = [];
    this.spinner.hide();
  }
}
